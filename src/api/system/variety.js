import request from '@/utils/request'

// 查询品类列表
export function listVariety(query) {
  return request({
    url: '/system/variety/list',
    method: 'get',
    params: query
  })
}

// 查询品类列表
export function listAllVariety(query) {
  return request({
    url: '/system/variety/listAll',
    method: 'get',
    params: query
  })
}

// 查询品类详细
export function getVariety(id) {
  return request({
    url: '/system/variety/' + id,
    method: 'get'
  })
}

// 新增品类
export function addVariety(data) {
  return request({
    url: '/system/variety',
    method: 'post',
    data: data
  })
}

// 修改品类
export function updateVariety(data) {
  return request({
    url: '/system/variety',
    method: 'put',
    data: data
  })
}

// 删除品类
export function delVariety(id) {
  return request({
    url: '/system/variety/' + id,
    method: 'delete'
  })
}
