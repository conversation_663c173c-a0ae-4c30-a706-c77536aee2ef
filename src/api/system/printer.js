import request from '@/utils/request'

// 查询打印机配置列表
export function listPrinter(query) {
  return request({
    url: '/system/printer/list',
    method: 'get',
    params: query
  })
}

// 查询打印机配置详细
export function getPrinter(id) {
  return request({
    url: '/system/printer/' + id,
    method: 'get'
  })
}

// 新增打印机配置
export function addPrinter(data) {
  return request({
    url: '/system/printer',
    method: 'post',
    data: data
  })
}

// 修改打印机配置
export function updatePrinter(data) {
  return request({
    url: '/system/printer',
    method: 'put',
    data: data
  })
}

// 删除打印机配置
export function delPrinter(id) {
  return request({
    url: '/system/printer/' + id,
    method: 'delete'
  })
}
