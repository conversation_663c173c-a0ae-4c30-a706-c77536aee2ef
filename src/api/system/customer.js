import request from '@/utils/request'

// 查询客户信息列表
export function listInfo(query) {
  return request({
    url: '/customer/info/list',
    method: 'get',
    params: query
  })
}

// 查询客户信息详细
export function getInfo(id) {
  return request({
    url: '/customer/info/' + id,
    method: 'get'
  })
}

// 新增客户信息
export function addInfo(data) {
  return request({
    url: '/customer/info/save',
    method: 'post',
    data: data
  })
}

// 修改客户信息
export function updateInfo(data) {
  return request({
    url: '/customer/info/update',
    method: 'put',
    data: data
  })
}

// 删除客户信息
export function delInfo(id) {
  return request({
    url: '/customer/info/' + id,
    method: 'delete'
  })
}

// 查询客户-用户关联列表
export function listRelation(query) {
  return request({
    url: '/customer/relation/list',
    method: 'get',
    params: query
  })
}

// 查询客户-用户关联详细
export function getRelation(id) {
  return request({
    url: '/customer/relation/' + id,
    method: 'get'
  })
}

// 新增客户-用户关联
export function addRelation(data) {
  return request({
    url: '/customer/relation/save',
    method: 'post',
    data: data
  })
}

// 修改客户-用户关联
export function updateRelation(data) {
  return request({
    url: '/customer/relation/update',
    method: 'put',
    data: data
  })
}

// 删除客户-用户关联
export function delRelation(id) {
  return request({
    url: '/customer/relation/' + id,
    method: 'delete'
  })
}
