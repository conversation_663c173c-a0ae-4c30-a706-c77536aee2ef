import request from '@/utils/request'

// 查询洗衣套餐列表
export function listPackages(query) {
  return request({
    url: '/system/packages/list',
    method: 'get',
    params: query
  })
}

// 查询洗衣套餐详细
export function getPackages(id) {
  return request({
    url: '/system/packages/' + id,
    method: 'get'
  })
}

// 新增洗衣套餐
export function addPackages(data) {
  return request({
    url: '/system/packages',
    method: 'post',
    data: data
  })
}

// 修改洗衣套餐
export function updatePackages(data) {
  return request({
    url: '/system/packages',
    method: 'put',
    data: data
  })
}

// 删除洗衣套餐
export function delPackages(id) {
  return request({
    url: '/system/packages/' + id,
    method: 'delete'
  })
}
