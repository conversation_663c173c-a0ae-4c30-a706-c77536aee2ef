import request from '@/utils/request'

export function uploadCos(data) {
    return request({
        url: '/common/uploadCos',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: data
    })
}


export function uploadCategoryCos(data) {
    return request({
        url: '/common/uploadCategoryCos',
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: data
    })
}

