<template>
  <div class="app-container">
    <div style="margin-bottom: 15px;">
      打印机状态：关闭
    </div>
    <el-descriptions class="margin-top" title="打印机设置" :column="2" :size="size" border>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            签收打印机名称
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            签收打印机配置
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
        <el-button style="margin-left: 30px;" type="primary">打印测试</el-button>
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            入厂打印机名称
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            入厂打印机配置
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
        <el-button style="margin-left: 30px;" type="primary">打印测试</el-button>
      </el-descriptions-item>


      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            包装打印机名称
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            包装打印机配置
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
        <el-button style="margin-left: 30px;" type="primary">打印测试</el-button>
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            拣货打印机名称
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            拣货打印机配置
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
        <el-button style="margin-left: 30px;" type="primary">打印测试</el-button>
      </el-descriptions-item>


      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            装箱打印机名称
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            装箱打印机配置
          </div>
        </template>
        <el-select style="width: 240px">
          <el-option>1</el-option>
        </el-select>
        <el-button style="margin-left: 30px;" type="primary">打印测试</el-button>
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label>
          <div class="cell-item">
            服务配置
          </div>
        </template>
        <el-input></el-input>
      </el-descriptions-item>
    </el-descriptions>

    <div style="margin-top: 18px; width: 100%; display: flex; justify-content: center;">
      <el-button type="primary">保存</el-button>
    </div>
  </div>
</template>

<script setup name="Printer">
import { ref } from 'vue'
const size = ref('default')
</script>
