<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类" prop="category">
        <el-select v-model="value" placeholder="请选择" style="width: 200px">
          <el-option key="0" label="全部" value="0" />
          <el-option key="1" label="衣服" value="1" />
          <el-option key="2" label="鞋靴" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="品类名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入品类名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="编码" prop="code">
        <el-input v-model="queryParams.code" placeholder="请输入编码" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:variety:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:variety:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:variety:remove']">删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:laundry:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="varietyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="#" align="center" prop="id" width="55" />
      <el-table-column label="物品名称" align="center" prop="name" />
      <el-table-column label="编码" align="center" prop="code" />
      <el-table-column label="分类" align="center" prop="category" >
        <template #default="scope">
          <el-tag v-if="scope.row.category == 'CLOTHES'">衣服</el-tag>
          <el-tag type="success" v-else>鞋靴</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="封面" align="center" prop="coverUrl" width="100">
        <template #default="scope">
          <div style="height: 80px; width: 80px;">
            <el-image :preview-teleported="true" :hide-on-click-modal="true" :preview-src-list="[scope.row.coverUrl]"
              :src="scope.row.coverUrl" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="seq" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-switch v-model="scope.row.status" style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            inline-prompt active-text="是" inactive-text="否" active-value="1" inactive-value="0" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:variety:edit']">修改</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)"
            v-hasPermi="['system:variety:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改品类对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="varietyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分类" prop="category">
          <el-input v-model="form.category" placeholder="请输入所属分类ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入物品名称" />
        </el-form-item>
        <el-form-item label="物品描述" prop="description">
          <el-input v-model="form.description" placeholder="请输入物品描述" />
        </el-form-item>
        <el-form-item label="排序" prop="seq">
          <el-input v-model="form.seq" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="封面" prop="coverUrl">
          <el-upload :class="{ disabled: noUpload }" :http-request="handleUploadCos" list-type="picture-card"
            :on-change="checkImageFormat" :on-remove="handleRemove" :limit="1" ref="businessLicense"
            :file-list="faceList">
            <el-icon >
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入编码" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Variety">
import { listVariety, getVariety, delVariety, addVariety, updateVariety } from "@/api/system/variety"
import { uploadCategoryCos } from "@/api/system/upload.js"

const { proxy } = getCurrentInstance()

const faceList = ref([])
const varietyList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const noUpload = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    category: null,
    name: null,
    description: null,
    seq: null,
    coverUrl: null,
    status: null,
    code: null
  },
  rules: {
    name: [
      { required: true, message: "物品名称不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询品类列表 */
function getList() {
  loading.value = true
  listVariety(queryParams.value).then(response => {
    varietyList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    category: null,
    name: null,
    description: null,
    seq: null,
    coverUrl: null,
    status: null,
    createTime: null,
    updateTime: null,
    code: null
  }
  proxy.resetForm("varietyRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加品类"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getVariety(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改品类"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["varietyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateVariety(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addVariety(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除品类编号为"' + _ids + '"的数据项？').then(function () {
    return delVariety(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/Variety/export', {
    ...queryParams.value
  }, `Variety_${new Date().getTime()}.xlsx`)
}


function handleUploadCos(param) {
  const formData = new FormData()
  formData.append('file', param.file)
  uploadCategoryCos(formData).then(res => {
    form.value.coverUrl = res.data.accessPath
  })
}

const validImageFormats = ["jpg", "jpeg", "png"];
//选择文件格式校验//并限制上传数量
const checkImageFormat = (file) => {
  // noUpload.value = true
  const fileFormat = file.name.split(".").pop().toLowerCase(); // 获取文件格式
  if (!validImageFormats.includes(fileFormat)) {
    ElMessage({ type: "error", message: "商品图片格式必须为 jpg/jpeg/png" });
    faceList.value = []; //删除格式不符合的文件
    return false; // 阻止文件上传
  }
  noUpload.value = true//设置为true阻止继续上传
  return true; // 允许文件上传
};

//删除
function handleRemove(file) {
  noUpload.value = false
}


getList()
</script>

<style scoped>
:deep(.disabled .el-upload--picture-card) {
  display: none !important;
}
</style>
