<template>
    <div class="app-container">
        <div>
            <el-row>
                <el-col :span="24">
                    <el-card shadow="never">
                        <div style="display: flex; align-items: center; margin-top: 10px;">
                            <div>
                                <el-text type="primary" size="large" tag="b">订单信息</el-text>
                            </div>
                            <el-input v-model="inNo" ref="scanInput" style="width: 300px; margin-left: 20px;"
                                placeholder="扫描或输入运单号或入厂单号" @keyup.enter="onScan">
                                <template #append><el-button :icon="FullScreen" /></template>
                            </el-input>
                            <el-tag style="margin-left: 20px;" size="large"
                                effect="dark">运单号:<span>{{ logisticsNoTag }}</span></el-tag>
                            <el-tag style="margin-left: 20px;" type="success" effect="dark"
                                size="large">订单号:<span>{{ orderNoTag }}</span></el-tag>

                            <div style="display: flex; align-items: center; margin-left: 20px;">
                                <span style="font-weight: bold;">已选择衣物:</span>
                                <div>
                                    <el-tag size="large" style="margin-left: 10px;" effect="dark">{{ selectedGoodName
                                    }}</el-tag>
                                </div>
                                <span style="margin-left: 20px; font-weight: bold;">已选择附件:</span>
                                <div>
                                    <el-tag size="large" style="margin-left: 10px;" effect="dark"
                                        v-for="item in selectedAnnexName">{{ item
                                        }}</el-tag>
                                </div>
                            </div>

                            <el-button type="success" style="margin-left: auto;">确认入场</el-button>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            <el-row style="margin-top: 10px;" :gutter="20">
                <el-col :span="6">
                    <el-card shadow="never">
                        <div style="display: flex; align-items: center; justify-content: space-between; ">
                            <el-text type="primary" size="large" tag="b">衣物列表</el-text>
                            <div>
                                <el-tag type="primary" effect="dark">进行中: {{ progressNum }} / {{ waitNum }}</el-tag>
                                <el-button type="primary" size="small" style="margin-left: 10px;">添加</el-button>
                            </div>
                        </div>
                        <el-scrollbar height="640px" @end-reached="loadMore" style="margin-top: 10px;">
                            <div class="good-info" v-for="(item, index) in clothList" :key="index"
                                :class="{ selected: selectedIndex === index }" @click="handelClothesItem(index)">
                                <div style="display: flex; align-items: center;">
                                    <el-text size="large" tag="b" type="danger">{{ index + 1 }}</el-text>
                                    <div
                                        style=" display: flex; flex-direction: column; align-items: flex-start;  margin-left: 18px;">
                                        <span :class="{ selectedItemColor: item.mainCode }"
                                            style=" font-weight: 600; ">{{
                                                item.spuName }}</span>
                                        <div v-if="item.mainCode">
                                            <span style="font-size: 14px;">{{ item.mainCode }}</span>
                                        </div>
                                        <div v-if="item.subCode">
                                            <span style="font-size: 14px;">{{ item.subCode }}</span>
                                        </div>
                                        <span style="font-size: 14px;">附件:</span>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center;">
                                    <img style="width: 18px; height: 18px;" src="@/assets/icons/svg/qk.svg" alt="">
                                    <img style="width: 32px; height: 32px; margin-left: 8px;"
                                        src="@/assets/icons/svg/dayin.svg" alt="">
                                </div>
                            </div>
                        </el-scrollbar>
                    </el-card>
                </el-col>
                <el-col :span="18">
                     <el-card shadow="never">
                        <el-steps :active="activeStep" simple style="margin-top: 8px;">
                            <el-step title="分类" :icon="Edit" @click="handleStepClick(0)" />
                            <el-step title="拍照" :icon="UploadFilled" @click="handleStepClick(1)" />
                            <el-step title="附件" :icon="Picture" @click="handleStepClick(2)" />
                        </el-steps>

                        <el-scrollbar height="620px" style="margin-top: 10px;"
                            v-if="activeStep === 0 && clothList?.length > 0">
                            <div style="margin-top: 12px;" v-for="item in clothingCategories">
                                <div style="color: #F56C6C; font-weight: bold;">
                                    <span v-if="item.type == 'CLOTHES'">[ 衣物 ]</span>
                                    <span v-else>[ 鞋靴 ]</span>
                                </div>
                                <div class="grid-wrapper">
                                    <el-row :gutter="10">
                                        <el-col :span="3" v-for="subItem in item.items" @click="handleGoodItem(subItem)"
                                            style="margin-top: 10px;">
                                            <div class="grid-item"
                                                :class="{ selectedGood: selectedGoodIndex === subItem.id }">
                                                <img :src="subItem.coverUrl" style="width: 80px; height: 80px;" alt="">
                                                <span style="margin-top: 8px; font-size: 14px;">{{ subItem.name
                                                    }}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                        </el-scrollbar>

                        <div v-if="activeStep === 1" style="margin-top: 10px;">
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <div v-if="!selectedPhoto">
                                        <el-button type="primary" size="small"
                                            @click="takePhoto">衣物拍照(Alt+2)</el-button>
                                        <el-button type="primary" size="small"
                                            @click="retractPhoto">撤回(Ctrz+z)</el-button>
                                        <el-text style="margin-left: 8px;">注意：点击大图后，可直接用鼠标在图上框选</el-text>
                                    </div>
                                    <el-button type="warning" size="small" @click="clearSelectedPhoto" v-else>
                                        返回摄像头
                                    </el-button>
                                    <el-button v-if="selectedPhoto" @click="openEditor">编辑图片</el-button>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <div class="camera-page">
                                        <div class="left-panel">
                                            <div class="preview-container">
                                                <div v-if="selectedPhoto">
                                                    <img :src="selectedPhoto" class="photo-preview" />
                                                    <div style="margin-top: 10px;">
                                                        <span style="font-size: 14px; font-weight: bold; ">风险描述</span>
                                                        <el-select v-model="value" placeholder="请选择"
                                                            style="width: 200px; margin-left: 10px;">
                                                            <el-option v-for="item in riskOptions" :key="item.value"
                                                                :label="item.label" :value="item.value" />
                                                        </el-select>
                                                    </div>
                                                    <div style="margin: 10px 0; display: flex; align-items: center;">
                                                        <el-input style="width: 500px;" v-model="textarea" :rows="1"
                                                            type="textarea" placeholder="备注" />
                                                        <el-button type="primary" size="small"
                                                            style="margin-top: 5px; margin-left: 10px; ">保存</el-button>
                                                    </div>
                                                </div>
                                                <video v-else ref="videoRef" autoplay playsinline  disablepictureinpicture
                                                    class="video-preview"></video>
                                            </div>
                                        </div>

                                        <div class="right-panel">
                                            <div class="photo-list">
                                                <div v-for="(photo, index) in photos" :key="index" class="photo-item"
                                                    @click="previewPhoto(photo)">
                                                    <img :src="photo" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>

                        <div v-if="activeStep === 2" style="margin-top: 10px;">
                            <div>
                                附件
                            </div>
                            <el-row :gutter="40">
                                <el-col :span="2" v-for="item in annexOptions" style="margin-top: 10px;">
                                    <el-button type="primary" size="large" plain>{{ item.name }}</el-button>
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 图片编辑器 -->
        <el-dialog v-model="editorVisible" center>
            <template #footer>
                <el-button @click="clearDrawing">清空标记</el-button>
                <el-button type="primary" @click="saveEditedImage">保存</el-button>
            </template>
            <div class="editor-container">
                <canvas ref="editorCanvas"></canvas>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="Entrance">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { listAllVariety } from "@/api/system/variety"
import { getScan, getWashCode } from "@/api/factory/produce"
import { FullScreen, Delete, Connection, Edit, UploadFilled, Picture } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance();
const orderNoTag = ref(null);
const logisticsNoTag = ref(null);
const scanInput = ref(null);
const selectedIndex = ref(null)
const selectedGoodIndex = ref(null)
const selectedGoodName = ref('无');
const selectedAnnexName = ref(['无']);
const activeStep = ref(0);
const value = ref('');
const inNo = ref('');
const clothList = ref([]);
const videoRef = ref(null);
const photos = ref([]);
const selectedLatelCodes= ref(null);
const selectedPhoto = ref(null);
const editorVisible = ref(false);
const editorCanvas = ref(null);
const progressNum = ref(0);
const waitNum = ref(0);
let stream = null;
let ctx, drawing = false, imgObj;

const riskOptions = [
    {
        value: '1',
        label: '破损/破洞，洗后可能会扩大',
    },
    {
        value: '2',
        label: '划伤，洗后可能会扩大',
    },
    {
        value: '3',
        label: '顽固油污，洗后可能无法完全去除',
    },
    {
        value: '4',
        label: '血渍，洗后可能无法完全去除',
    },
    {
        value: '5',
        label: '涂层，洗后可能会脱落',
    },
]

const annexOptions = [
  { "id": "1", "name": "毛领" },
  { "id": "2", "name": "帽子" },
  { "id": "3", "name": "内胆" },
  { "id": "4", "name": "腰带" },
  { "id": "5", "name": "披肩" },
  { "id": "6", "name": "活袖" },
  { "id": "7", "name": "鞋带" },
  { "id": "8", "name": "鞋垫" },
  { "id": "9", "name": "肩带" },
  { "id": "10", "name": "扣子" },
  { "id": "11", "name": "其他" }
]

const clothingCategories = ref([])

/**
 * 点击衣物列表
 */
function handelClothesItem(index) {
    selectedIndex.value = index
}

//步骤条点击事件
function handleStepClick(index) {

    if (clothList.value?.length <= 0) {
        proxy.$modal.msgWarning("请先扫描快递面单或入场单号")
        return
    }
    activeStep.value = index

    if (index == 1) {
        selectedPhoto.value = null;
        startCamera();
    } else {
        stream.getTracks().forEach(track => track.stop());
    }

}

function handleGoodItem(item) {

    let index = selectedIndex.value;

    if (index == null) {
        proxy.$modal.msgWarning("请先选择衣物");
        return
    }
    selectedGoodName.value = item.name
    selectedGoodIndex.value = item.id
    if (index >= 0 && index < clothList.value.length) {
        clothList.value[index].spuName = selectedGoodName.value;
    }
    getWashCode(item.category).then(res => {

        if(res.data.mainCode){
            selectedLatelCodes.value = res.data.mainCode;
            clothList.value[index].mainCode = res.data.mainCode;
        }

        if(res.data.subCode){
            clothList.value[index].subCode = res.data.subCode;
        }

        progressNum.value = progressNum.value + 1;     
    })

    activeStep.value = activeStep.value + 1
}

function getCategory() {
    listAllVariety().then(res => {
        clothingCategories.value = res.data
    })
}

function onScan() {
    getScan(inNo.value).then(res => {
        clothList.value = res.data.item
        waitNum.value = res.data.item.length
        activeStep.value = 0
        orderNoTag.value = res.data.orderNo
        logisticsNoTag.value = res.data.logisticsNo
        selectedIndex.value = null
        selectedGoodIndex.value = null
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
    })
}

const startCamera = async () => {
    try {
        stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
        videoRef.value.srcObject = stream;
    } catch (err) {
        proxy.$modal.msgWarning("无法访问摄像头，请检查连接或浏览器权限: " + err.message);
    }
};

const takePhoto = () => {

    if (!videoRef.value) return;

    if(!selectedLatelCodes.value) return;

    const dpr = window.devicePixelRatio || 1;
    const videoWidth = videoRef.value.videoWidth;
    const videoHeight = videoRef.value.videoHeight;

    const canvas = document.createElement("canvas");
    canvas.width = videoWidth * dpr;
    canvas.height = videoHeight * dpr;

    const ctx = canvas.getContext("2d");
    ctx.scale(dpr, dpr);

    // 先画摄像头画面
    ctx.drawImage(videoRef.value, 0, 0, videoWidth, videoHeight);

    // 公共字体样式
    ctx.font = "22px Microsoft YaHei"; // 中文微软雅黑

    // ===== 左下角：拍摄时间 =====
    const timeText = new Date().toLocaleString();
    ctx.textAlign = "right";
    ctx.textBaseline = "bottom";
    ctx.fillStyle = "white";
    ctx.fillText(timeText, videoWidth - 20, videoHeight - 20);

    // ===== 左上角：订单号 =====
    ctx.textAlign = "left";
    ctx.textBaseline = "top";
    ctx.fillStyle = "white";
    ctx.fillText(orderNoTag.value, 20, 20);

    // ===== 右上角：水洗码 =====
    ctx.textAlign = "right";
    ctx.textBaseline = "top";
    ctx.fillStyle = "white";
    ctx.fillText(selectedLatelCodes.value, videoWidth - 30, 20);

    // 生成高清图片
    const photoData = canvas.toDataURL("image/png");
    photos.value.unshift(photoData);
};

// 打开编辑器
const openEditor = () => {
    editorVisible.value = true;
    setTimeout(() => {
        const canvas = editorCanvas.value;
        ctx = canvas.getContext("2d");

        imgObj = new Image();
        imgObj.src = selectedPhoto.value;
        imgObj.onload = () => {
            canvas.width = imgObj.width;
            canvas.height = imgObj.height;
            ctx.drawImage(imgObj, 0, 0);
        };

        // 绑定绘图事件
        canvas.onmousedown = (e) => {
            drawing = true;
            ctx.beginPath();
            ctx.moveTo(e.offsetX, e.offsetY);
        };
        canvas.onmousemove = (e) => {
            if (drawing) {
                ctx.lineTo(e.offsetX, e.offsetY);
                ctx.strokeStyle = "red";
                ctx.lineWidth = 10;
                ctx.stroke();
            }
        };
        canvas.onmouseup = () => {
            drawing = false;
        };
    }, 50);
};

// 清空标记
const clearDrawing = () => {
    ctx.drawImage(imgObj, 0, 0);
};

// 保存编辑后的图片
const saveEditedImage = () => {
    const newPhoto = editorCanvas.value.toDataURL("image/png");
    selectedPhoto.value = newPhoto;
    photos.value = photos.value.map(p => p === selectedPhoto.value ? newPhoto : p);
    editorVisible.value = false;
};

const retractPhoto = () => {
    photos.value.pop();
}

// 点击预览照片
const previewPhoto = (photo) => {
    selectedPhoto.value = photo;
};

// 返回摄像头
const clearSelectedPhoto = () => {
    startCamera();
    selectedPhoto.value = null;
};

// 快捷键监听
const handleKeydown = (e) => {
    // Alt + 2  => 拍照
    if (e.altKey && e.key === "2") {
        e.preventDefault();
        takePhoto();
    }
    // Ctrl + Z => 撤回
    if (e.ctrlKey && (e.key === "z" || e.key === "Z")) {
        e.preventDefault();
        retractPhoto();
    }
};


onMounted(async () => {
    await nextTick();
    scanInput.value.focus();
    window.addEventListener("keydown", handleKeydown);
});

onBeforeUnmount(() => {
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
    }
    window.removeEventListener("keydown", handleKeydown);
});


watch(activeStep, (newVal, oldVal) => {
    if (newVal == 1) {
        startCamera(newVal)
    }
})

getCategory();
</script>


<style lang="scss" scoped>
.good-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f5f7fa;
    padding: 12px 18px;
    border-radius: 6px;
    margin-top: 12px;
}

.good-info:first-child {
    margin-top: 0;
}

.good-info:hover {
    background-color: rgb(244, 244, 245);
    cursor: pointer;
}

.good-info.selected {
    background-color: #f8f5cb;

}


::v-deep(.el-scrollbar__bar) {
    width: 2px;
    overflow: hidden;
}

.grid-wrapper {
    margin: 0 auto;

}

.grid-item {
    height: 140px;
    width: 140px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    font-size: 16px;
    border-radius: 4px;
    transition: all 0.3s;
    padding: 10px;
}

.grid-item:hover {
    background-color: rgb(233, 233, 235);
    cursor: pointer;
}

.grid-item.selectedGood {
    background-color: #f8f4c9;
    font-weight: bold;
}

.camera-page {
    display: flex;
    height: 100%;
    box-sizing: border-box;
}

/* 左侧面板 */
.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    margin-top: 8px;
}

.preview-container {
    max-height: 580px;
    display: flex;
    justify-content: center;
}

.photo-preview {
    height: 480px;
    width: 100%;
    object-fit: cover;
}

.video-preview {
    height: 565px;
    width: 100%;
    object-fit: cover;
}

/* 右侧面板 */
.right-panel {
    width: 500px;
    max-height: 550px;
    margin-left: 10px;
    overflow-y: auto;
    box-sizing: border-box;
    margin-top: 8px;
}

.photo-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.photo-item {
    width: 100%;
    cursor: pointer;
}

.photo-item img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #ddd;
}

.editor-container {
    display: flex;
    justify-content: center;
}

.selectedItemColor {
    color: #be0e0e;
}

::v-deep(.el-dialog.el-dialog--center) {
    background-color: transparent;
    box-shadow: none;
    padding: 0;
}
</style>