<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <div style="display: flex; align-items: center;">
          <el-input v-model="input2" style="width: 240px; " placeholder="请输入或扫描水洗码" :prefix-icon="Search" />
          <el-button type="primary" style="margin-left: 10px;">查询</el-button>
          <el-input v-model="input3" style="width: 240px; margin-left: 10px;" placeholder="请输入或扫描附件码" :prefix-icon="Search" />
          <el-button type="primary" style="margin-left: 10px;">查询</el-button>
        </div>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="never">
            <div>
              <el-descriptions
                title="衣物信息"
                :column="5"
                :size="size"
                direction="vertical"
                :style="blockMargin"
              >
                <el-descriptions-item label="衣物名称">旅游鞋任意洗一双</el-descriptions-item>
                <el-descriptions-item label="水洗码">18100000000</el-descriptions-item>
                <el-descriptions-item label="入场ID">1001</el-descriptions-item>
                <el-descriptions-item label="衣物格架号">
                  6
                </el-descriptions-item>
                <el-descriptions-item label="订单号">
                  100001
                </el-descriptions-item>
              </el-descriptions>
            </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
            <div>
              <el-descriptions
                title="衣物附件信息"
                :column="5"
                :size="size"
                direction="vertical"
                :style="blockMargin"
              >
                <el-descriptions-item label="附件">旅游鞋任意洗一双</el-descriptions-item>
                <el-descriptions-item label="附件格架号">18100000000</el-descriptions-item>
                <el-descriptions-item label="附件数量：">1001</el-descriptions-item>
              </el-descriptions>
            </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-table :data="tableData" border stripe style="width: 100%">
          <el-table-column prop="date" label="附件名称"  align="center"/>
          <el-table-column prop="name" label="格架号"  align="center"/>
          <el-table-column prop="address" label="是否扫描"  align="center"/>
        </el-table>
        <div style="display: flex; justify-content: end; margin-top: 20px;">
          <el-pagination
            size="small"
            background
            layout="prev, pager, next"
            :total="50"
          />
        </div>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <div style="display: flex; justify-content: center; margin-top: 20px;">
          <el-button type="danger" style="width: 120px;" size="large">丢件</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const feature = ref();

const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
]
</script>
