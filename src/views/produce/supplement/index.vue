<template>
    <div class="app-container">
        <el-row>
            <el-col :span="24">
                <div style="display: flex; align-items: center;">
                    <el-input v-model="input2" style="width: 340px; " placeholder="请输入或扫描水洗码" :prefix-icon="Search" />
                    <el-button type="primary" style="margin-left: 10px;">查询</el-button>
                    <div style="margin-left: auto;">
                        <el-button type="danger">拍照(Alt+2)</el-button>
                        <el-button type="danger" style="margin-left: 10px;">反洗</el-button>
                        <el-button type="danger" style="margin-left: 10px;">在厂售后</el-button>
                        <el-button type="danger" style="margin-left: 10px;">污渍终止</el-button>
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-divider />
        <el-row :gutter="20">
            <el-col :span="8">
                <el-card shadow="never" style="height: 720px;">
                    <el-descriptions title="订单信息" :column="2" direction="vertical">
                        <el-descriptions-item label="订单号" label-class-name="yw-label"
                            class-name="yw-content">18100000000</el-descriptions-item>
                        <el-descriptions-item label="水洗码" label-class-name="yw-label"
                            class-name="yw-content">11111111111</el-descriptions-item>

                    </el-descriptions>
                    <el-divider />
                    <el-descriptions title="衣物基本信息" :column="2" direction="vertical">
                        <el-descriptions-item label="客户姓名" label-class-name="yw-label"
                            class-name="yw-content">张三</el-descriptions-item>
                        <el-descriptions-item label="联系方式" label-class-name="yw-label"
                            class-name="yw-content">18100000000</el-descriptions-item>
                        <el-descriptions-item label="水洗码" label-class-name="yw-label"
                            class-name="yw-content">10001</el-descriptions-item>
                        <el-descriptions-item label="入场单号" label-class-name="yw-label" class-name="yw-content">
                            <el-tag size="small">100001</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="衣物名称" label-class-name="yw-label"
                            class-name="yw-content">羊毛大衣</el-descriptions-item>
                        <el-descriptions-item label="衣物名称" label-class-name="yw-label"
                            class-name="yw-content">羊毛大衣</el-descriptions-item>
                        <el-descriptions-item label="用户备注" label-class-name="yw-user-label"
                            class-name="yw-content">衣领处深度清洗衣领处深度清洗衣领处深度清洗</el-descriptions-item>
                    </el-descriptions>
                </el-card>
            </el-col>
            <el-col :span="16">
                <el-card shadow="never" style="height: 720px;">
                    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
                        <el-tab-pane label="补拍照片" name="first">
                            <el-row :gutter="20">
                                <el-col :span="16" style="">
                                    <div style="background-color: #000; height: 420px;">
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <el-scrollbar height="420px" @end-reached="loadMore">
                                        <el-row :gutter="10">
                                            <el-col class="zjpto" :span="12" v-for="item in 10">
                                                <div
                                                    style="height: 100px; background-color: #F56C6C; margin-bottom: 10px;">
                                                </div>
                                            </el-col>
                                        </el-row>
                                    </el-scrollbar>
                                </el-col>
                            </el-row>
                            <div style="display: flex; align-items: center; justify-content: center; margin-top: 40px;">
                                <el-button type="primary" size="large">补拍完成</el-button>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="补录附件" name="second">
                            <div>
                                已选择的附件: 毛领
                            </div>
                            <div style="margin-top: 10px;">
                                <el-row :gutter="20">
                                    <el-col :span="2" v-for="item in 20" style="margin-top: 10px;">
                                        <el-button type="primary" plain>毛领</el-button>
                                    </el-col>
                                </el-row>
                            </div>
                            <div style="display: flex; align-items: center; justify-content: center; margin-top: 40px;">
                                <el-button type="primary" size="large">打印标签</el-button>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { Search } from '@element-plus/icons-vue'
const feature = ref();
const activeName = ref('first')

const tableData = [
    {
        date: '2016-05-03',
        name: 'Tom',
        address: '张三',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        address: '张三',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        address: '张三',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        address: '张三',
    },
]
</script>


<style lang="scss" scoped>
:deep(.yw-label) {
    color: #6b7280;
}

:deep(.yw-user-label) {
    color: #F56C6C;
}

:deep(.yw-content) {
    color: #000;
    font-weight: bold;
}

:deep(.el-divider--horizontal) {
    margin: 12px 0;
}
</style>