<template>
  <div class="app-container">
      <div class="qshc-centent">
          <h1>包装</h1>
          <el-input v-model="input1" style="width: 380px" size="large" placeholder="请输入或扫描水洗码"
              :suffix-icon="Search" />
          <el-button type="primary" style="margin-top: 20px;width: 380px" size="large">确定</el-button>
      </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Search } from '@element-plus/icons-vue'
const feature = ref();
</script>

<style scoped>
.qshc-centent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100px;
  h1 {
      font-size: 28px;
      font-weight: bold;
      color: #409EFF;
  }
}
</style>