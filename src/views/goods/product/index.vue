<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="所属分类" prop="category">
        <el-input v-model="queryParams.category" placeholder="请输入所属分类" clearable @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:product:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:product:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:product:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:product:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id"  width="55" />
      <el-table-column label="商品名称" align="center" prop="name" width="150"/>
      <el-table-column label="商品图片" align="center" prop="imageUrl">
        <template #default="scope">
          <div style="height: 60px; width: 60px;">
            <el-image :preview-teleported="true" :hide-on-click-modal="true" :preview-src-list="[scope.row.imageUrl]"
              :src="scope.row.imageUrl" />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="分类" align="center" prop="category" >
        <template #default="scope">
          <el-tag v-if="scope.row.category== 'SHOES'">鞋靴</el-tag>
          <el-tag v-else type="success">衣服</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="普洗原价" align="center" prop="regularPrice" />
      <el-table-column label="普洗优惠价" align="center" prop="regularDiscountPrice" />
      <el-table-column label="奢洗原价" align="center" prop="premiumPrice" />
      <el-table-column label="奢洗优惠价" align="center" prop="premiumDiscountPrice" />
      <el-table-column label="销量" align="center" prop="saleNum" />
      <el-table-column label="是否上架" align="center" prop="status">
        <template #default="scope">
          <el-switch v-model="scope.row.status" style="--el-switch-off-color: #ff4949"
            inline-prompt active-text="上架" inactive-text="下架" active-value="1" inactive-value="0" @change="(value) => onChangeSwitch(value, scope.row.id)"/>
        </template>
      </el-table-column>
      <el-table-column label="商品说明" align="center" prop="description">
        <template #default="scope">
          <el-button link type="primary" @click="desClick(scope.row.description)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="排序字段" align="center" prop="sortOrder" />
      <el-table-column label="创建时间" align="center" prop="createdAt" >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" >
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" >
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:product:edit']">修改</el-button> 
          <el-button link type="primary"  @click="handleDelete(scope.row)"
            v-hasPermi="['system:product:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改商品对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="productRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品图片:" prop="imageUrl">
          <el-upload :class="{ disabled: noUpload }" :http-request="handleUploadCos" list-type="picture-card"
            :on-change="checkImageFormat" :on-remove="handleRemove" :limit="1" ref="businessLicense"
            :file-list="faceList">
            <el-icon v-if="faceList.length == 0">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="普洗原价" prop="regularPrice">
          <el-input-number v-model="form.regularPrice" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="普洗优惠价" prop="regularDiscountPrice">
          <el-input-number v-model="form.regularDiscountPrice" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="奢洗原价" prop="premiumPrice">
          <el-input-number v-model="form.premiumPrice" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="奢洗优惠价" prop="premiumDiscountPrice">
          <el-input-number v-model="form.premiumDiscountPrice" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="销量" prop="saleNum">
          <el-input-number v-model="form.saleNum" :min="1" :max="9999" @change="handleChange" />
        </el-form-item>
        <el-form-item label="所属分类" prop="category">
          <el-select v-model="form.category" placeholder="请输入所属分类" style="width: 180px">
            <el-option
              key="CLOTHES"
              label="衣服"
              value="CLOTHES"
            />
            <el-option
              key="SHOES"
              label="鞋靴"
              value="SHOES"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品说明" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisible" width="50%" style="background-color: transparent; box-shadow: none;">
      <img w-full :src="dialogImageUrl" alt="Preview Image"
        style="max-width: 100%; max-height: 80vh; display: block; margin: 0 auto;" />
    </el-dialog>
  </div>
</template>

<script setup name="Product">
import { listProduct, getProduct, delProduct, addProduct, updateProduct, apiUpdateStatus } from "@/api/system/product"
import { uploadCos } from "@/api/system/upload"

const { proxy } = getCurrentInstance()

const faceList = ref([])
const productList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const noUpload = ref(false)


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    imageUrl: null,
    description: null,
    status: null,
    sortOrder: null,
    createdAt: null,
    updatedAt: null,
    category: null,
    marketPrice: null,
    saleNum: null,
    regularPrice: null,
    regularDiscountPrice: null,
    premiumPrice: null,
    premiumDiscountPrice: null
  },
  rules: {
    name: [
      { required: true, message: "商品名称不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询商品列表 */
function getList() {
  loading.value = true
  listProduct(queryParams.value).then(response => {
    productList.value = response.rows
    total.value = response.total
    loading.value = false
    
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    imageUrl: null,
    description: null,
    status: null,
    sortOrder: null,
    createdAt: null,
    updatedAt: null,
    category: null,
    marketPrice: null,
    saleNum: null,
    regularPrice: null,
    regularDiscountPrice: null,
    premiumPrice: null,
    premiumDiscountPrice: null
  }
  proxy.resetForm("productRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  faceList.value = [];
  noUpload.value = false
  open.value = true
  title.value = "添加商品"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getProduct(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改商品"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["productRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateProduct(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addProduct(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除商品编号为"' + _ids + '"的数据项？').then(function () {
    return delProduct(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/product/export', {
    ...queryParams.value
  }, `product_${new Date().getTime()}.xlsx`)
}


const validImageFormats = ["jpg", "jpeg", "png"];//允许的文件后缀
//选择文件格式校验//并限制上传数量
const checkImageFormat = (file) => {
  // noUpload.value = true
  const fileFormat = file.name.split(".").pop().toLowerCase(); // 获取文件格式
  if (!validImageFormats.includes(fileFormat)) {
    ElMessage({ type: "error", message: "商品图片格式必须为 jpg/jpeg/png" });
    faceList.value = []; //删除格式不符合的文件
    return false; // 阻止文件上传
  }
  noUpload.value = true//设置为true阻止继续上传
  return true; // 允许文件上传
};

const dialogImageUrl = ref(""); //预览图片路径
const dialogVisible = ref(false); //预览框可见


//删除
function handleRemove(file) {
  noUpload.value = false
}

//预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

function handleUploadCos(param) {
  const formData = new FormData()
  formData.append('file', param.file)
  uploadCos(formData).then(res => {
    form.value.imageUrl = res.data.accessPath
  })
}

function onChangeSwitch(newStatus, id) {
  let params = {
    id: id,
    status: newStatus
  }
  apiUpdateStatus(params).then(res => {
    if(res.code == 200){
      const msg = newStatus == 1 ? "上架成功" : "下架成功";
      proxy.$modal.msgSuccess(msg);
    }
  })
}

getList()
</script>


<style scoped>
:deep(.disabled .el-upload--picture-card) {
  display: none !important;

}
</style>
