<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="套餐名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:packages:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:packages:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:packages:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:packages:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="packagesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="套餐ID" align="center" prop="id" />
      <el-table-column label="套餐名称" align="center" prop="packageName" />
      <el-table-column label="套餐封面" align="center" prop="coverUrl" width="90">
        <template #default="scope">
          <div style="height: 80px; width: 80px; ">
            <el-image :preview-teleported="true" :hide-on-click-modal="true" :preview-src-list="[scope.row.coverUrl]"
              :src="scope.row.coverUrl" />
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="套餐价格" align="center" prop="price" />
      <el-table-column label="优惠价" align="center" prop="discountPrice" />
      <el-table-column label="套餐有效期" align="center" prop="durationDays" />
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="套餐描述" align="center" prop="description" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['system:packages:edit']">修改</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)" v-hasPermi="['system:packages:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改洗衣套餐对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="packagesRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="套餐名称" prop="packageName">
          <el-input v-model="form.packageName" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="商品图片:" prop="coverUrl">
          <el-upload :class="{ disabled: noUpload }" :http-request="handleUploadCos" list-type="picture-card"
            :on-change="checkImageFormat" :on-remove="handleRemove" :limit="1" ref="businessLicense"
            :file-list="faceList">
            <el-icon v-if="faceList.length == 0">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="适用衣物" prop="clothesNum">
          <el-input-number v-model="form.clothesNum" :min="1">
            <template #suffix>
              <span>件</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="适用鞋靴" prop="shoesNum">
          <el-input-number v-model="form.shoesNum" :min="1">
            <template #suffix>
              <span>双</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="原价" prop="price">
          <el-input-number v-model="form.price" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="优惠价" prop="discountPrice">
          <el-input-number v-model="form.discountPrice" :precision="2" :min="1">
            <template #suffix>
              <span>￥</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="有效期" prop="durationDays">
          <el-input v-model="form.durationDays" type="number" placeholder="请输入套餐有效期，单位天" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Packages">
import { listPackages, getPackages, delPackages, addPackages, updatePackages } from "@/api/system/packages"
import { uploadCos } from "@/api/system/upload"
const { proxy } = getCurrentInstance()

const faceList = ref([])
const packagesList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const noUpload = ref(false)
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    packageName: null,
    description: null,
    price: null,
    discountPrice: null,
    durationDays: null,
    createdAt: null,
    updatedAt: null
  },
  rules: {
    packageName: [
      { required: true, message: "套餐名称不能为空", trigger: "blur" }
    ],
    price: [
      { required: true, message: "套餐价格不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询洗衣套餐列表 */
function getList() {
  loading.value = true
  listPackages(queryParams.value).then(response => {
    packagesList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    packageName: null,
    description: null,
    price: null,
    discountPrice: null,
    durationDays: null,
    createdAt: null,
    updatedAt: null
  }
  proxy.resetForm("packagesRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加洗衣套餐"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getPackages(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改洗衣套餐"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["packagesRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePackages(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addPackages(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除洗衣套餐编号为"' + _ids + '"的数据项？').then(function() {
    return delPackages(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/packages/export', {
    ...queryParams.value
  }, `packages_${new Date().getTime()}.xlsx`)
}

const validImageFormats = ["jpg", "jpeg", "png"];//允许的文件后缀
//选择文件格式校验//并限制上传数量
const checkImageFormat = (file) => {
  // noUpload.value = true
  const fileFormat = file.name.split(".").pop().toLowerCase(); // 获取文件格式
  if (!validImageFormats.includes(fileFormat)) {
    ElMessage({ type: "error", message: "商品图片格式必须为 jpg/jpeg/png" });
    faceList.value = []; //删除格式不符合的文件
    return false; // 阻止文件上传
  }
  noUpload.value = true//设置为true阻止继续上传
  return true; // 允许文件上传
};

const dialogImageUrl = ref(""); //预览图片路径
const dialogVisible = ref(false); //预览框可见


//删除
function handleRemove(file) {
  noUpload.value = false
}

//预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

function handleUploadCos(param) {
  const formData = new FormData()
  formData.append('file', param.file)
  uploadCos(formData).then(res => {
    form.value.coverUrl = res.data.accessPath
  })
}

getList()
</script>

<style scoped>
:deep(.disabled .el-upload--picture-card) {
  display: none !important;

}
</style>

