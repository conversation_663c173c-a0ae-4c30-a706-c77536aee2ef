<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" >
          <el-form-item label="订单编号" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="物流单号" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="订单来源" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="用户手机号" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="水洗码" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search">查询</el-button>
            <el-button icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-table :data="tableData" border style="width: 100%;">
          <el-table-column prop="name" label="操作" />
          <el-table-column prop="name" label="序号" />
          <el-table-column prop="name" label="订单编号" />
          <el-table-column prop="name" label="原订单编号" />
          <el-table-column prop="name" label="城市" />
          <el-table-column prop="name" label="用户手机号" />
          <el-table-column prop="name" label="订单状态" />
          <el-table-column prop="name" label="订单来源" />
          <el-table-column prop="name" label="下单衣物数量" />
          <el-table-column prop="name" label="实际衣物数量" />
          <el-table-column prop="name" label="用户备注" />
          <el-table-column prop="name" label="送衣时间" />
          <el-table-column prop="name" label="创建时间" />
        </el-table>
        <el-pagination style="margin-top: 10px;" v-model:current-page="currentPage4" v-model:page-size="pageSize4"
          :page-sizes="[100, 200, 300, 400]" :size="size" :disabled="disabled" :background="background"
          layout="total, sizes, prev, pager, next, jumper" :total="400" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const currentPage4 = ref(4)
const pageSize4 = ref(100)
const radio2 = ref('1')
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined
  },
  rules: {
    userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }, { min: 2, max: 20, message: "用户名称长度必须介于 2 和 20 之间", trigger: "blur" }],
    nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
    password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }, { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" }, { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }],
    email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
    phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
  }


})


const { queryParams, form, rules } = toRefs(data)

const handleSizeChange = () => {
  console.log(`${val} items per page`)
}
const handleCurrentChange = () => {
  console.log(`current page: ${val}`)
}

</script>