<template>
  <div class="group-admin-container">
    <div class="layout-wrapper">
      <!-- 左侧客户树 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>客户管理</h3>
          <el-button type="primary" size="small" @click="showAddGroupDialog = true">
            <el-icon><Plus /></el-icon>
            新增客户
          </el-button>
        </div>
        <div class="tree-container">
          <el-tree
            ref="groupTreeRef"
            :data="groupTreeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handleGroupSelect"
            :highlight-current="true"
            default-expand-all
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <div class="node-content">
                  <el-icon><Folder /></el-icon>
                  <span class="node-label">{{ node.label }}</span>
                  <span class="user-count">({{ data.userCount }}人)</span>
                </div>
                <div class="node-actions">
                  <el-button type="text" size="small" @click.stop="editGroup(data)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="text" size="small" @click.stop="deleteGroup(data)" class="danger-text">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 中间用户列表 -->
      <div class="middle-panel">
        <div class="panel-header">
          <h3>用户列表</h3>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户"
              style="width: 200px; margin-right: 10px"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button v-if="selectedGroupId" type="info" size="small" @click="clearGroupSelection">
              <el-icon><Close /></el-icon>
              清空筛选
            </el-button>
          </div>
        </div>
        <div class="user-list-container">
          <el-table
            :data="paginatedUserList"
            @selection-change="handleUserSelection"
            @row-click="handleUserClick"
            height="calc(100% - 60px)"
            v-loading="loading"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="avatar" label="头像" width="80">
              <template #default="{ row }">
                <el-avatar :src="row.avatarUrl" :size="40">{{ row.nickname ? row.nickname.charAt(0) : 'U' }}</el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="昵称" min-width="120" />
            <el-table-column prop="phone" label="手机号" min-width="130" />
            <el-table-column prop="gender" label="性别" width="80">
              <template #default="{ row }">
                <span>{{ row.gender === 1 ? '男' : row.gender === 2 ? '女' : '未知' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="balance" label="余额" width="100">
              <template #default="{ row }">
                <span class="balance">¥{{ (row.balance || 0).toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分" width="80" />
            <el-table-column prop="memberLevel" label="会员等级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.memberLevel === 0 ? 'info' : row.memberLevel === 1 ? 'warning' : 'success'" size="small">
                  {{ row.memberLevel === 0 ? '普通' : row.memberLevel === 1 ? '黄金' : row.memberLevel === 2 ? '白金' : '高级' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="groupName" label="所属客户" min-width="120">
              <template #default="{ row }">
                <el-tag v-if="row.groupName" type="info" size="small">{{ row.groupName }}</el-tag>
                <el-tag v-else type="warning" size="small">未分配</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                  {{ row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="100" />
            <el-table-column prop="registerTime" label="注册时间" min-width="160" />
            <el-table-column prop="lastLoginTime" label="最后登录" min-width="160" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="text" size="small" @click.stop="assignCustomer(row)">分配客户</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="filteredUserList.length"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>

      <!-- 右侧管理面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>管理面板</h3>
        </div>
        <div class="management-content">
          <!-- 选中用户信息 -->
          <div class="selected-user-info" v-if="selectedUser && selectedUsers.length <= 1">
            <el-card shadow="never">
              <div class="user-info-header">
                <el-avatar :src="selectedUser.avatar" :size="60">
                  {{ selectedUser.name.charAt(0) }}
                </el-avatar>
                <div class="user-details">
                  <h4>{{ selectedUser.name }}</h4>
                  <p>{{ selectedUser.phone }}</p>
                  <p>余额: <span class="balance">¥{{ selectedUser.balance.toFixed(2) }}</span></p>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 批量操作区域 -->
          <div class="batch-operations" v-if="selectedUsers.length > 1">
            <el-card shadow="never">
              <div slot="header" class="batch-header">
                <span>批量操作 (已选择{{ selectedUsers.length }}个用户)</span>
              </div>
              <div class="batch-actions">
                <el-button type="primary" @click="showRechargeDialog = true">
                  <el-icon><Money /></el-icon>
                  批量充值
                </el-button>
                <el-button type="warning" @click="batchToggleStatus('disabled')">
                  <el-icon><Lock /></el-icon>
                  批量禁用
                </el-button>
                <el-button type="success" @click="batchToggleStatus('active')">
                  <el-icon><Unlock /></el-icon>
                  批量启用
                </el-button>
              </div>
            </el-card>
          </div>

          <!-- 单用户操作区域 -->
          <div class="single-operations" v-if="selectedUser && selectedUsers.length <= 1">
            <el-card shadow="never">
              <div slot="header" class="single-header">
                <span>用户操作</span>
              </div>
              <div class="single-actions">
                <el-button type="primary" @click="showSingleRechargeDialog = true" block>
                  <el-icon><Money /></el-icon>
                  充值余额
                </el-button>
                <!-- <el-button 
                  :type="selectedUser.status === 'active' ? 'warning' : 'success'" 
                  @click="toggleUserStatus(selectedUser)"
                  block
                >
                  <el-icon v-if="selectedUser.status === 'active'"><Lock /></el-icon>
                  <el-icon v-else><Unlock /></el-icon>
                  {{ selectedUser.status === 'active' ? '禁用用户' : '启用用户' }}
                </el-button> -->
                <el-button type="info" @click="viewUserHistory" block>
                  <el-icon><Document /></el-icon>
                  查看记录
                </el-button>
              </div>
            </el-card>
          </div>

          <!-- 统计信息 -->
          <div class="statistics">
            <el-card shadow="never">
              <div slot="header">
                <span>客户统计</span>
              </div>
              <div class="stats-content" v-if="currentGroupStats">
                <div class="stat-item">
                  <span class="label">总用户数:</span>
                  <span class="value">{{ currentGroupStats.totalUsers }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">活跃用户:</span>
                  <span class="value success">{{ currentGroupStats.activeUsers }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">禁用用户:</span>
                  <span class="value danger">{{ currentGroupStats.disabledUsers }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">总余额:</span>
                  <span class="value balance">¥{{ currentGroupStats.totalBalance.toFixed(2) }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增客户对话框 -->
    <el-dialog
      v-model="showAddGroupDialog"
      title="新增客户"
      width="500px"
      @close="resetGroupForm"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="客户名称:" prop="name">
          <el-input
            v-model="groupForm.name"
            placeholder="请输入客户名称"
          />
        </el-form-item>
        <el-form-item label="客户描述:">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入客户描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAddGroup">确认新增</el-button>
      </template>
    </el-dialog>

    <!-- 编辑客户对话框 -->
    <el-dialog
      v-model="showEditGroupDialog"
      title="编辑客户"
      width="500px"
      @close="resetGroupForm"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="客户名称:" prop="name">
          <el-input
            v-model="groupForm.name"
            placeholder="请输入客户名称"
          />
        </el-form-item>
        <el-form-item label="客户描述:">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入客户描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmEditGroup">确认修改</el-button>
      </template>
    </el-dialog>

    <!-- 分配客户对话框 -->
    <el-dialog
      v-model="showAssignCustomerDialog"
      title="分配客户"
      width="500px"
      @close="resetAssignCustomerForm"
    >
      <el-form :model="assignCustomerForm" :rules="assignCustomerRules" ref="assignCustomerFormRef" label-width="100px">
        <el-form-item label="用户信息:">
          <div>
            <p><strong>昵称:</strong> {{ assignCustomerForm.userName }}</p>
            <p><strong>手机号:</strong> {{ assignCustomerForm.userPhone }}</p>
          </div>
        </el-form-item>
        <el-form-item label="选择客户:" prop="customerId">
          <el-select
            v-model="assignCustomerForm.customerId"
            placeholder="请选择客户"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="group in groupTreeData"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="赠送金额:" prop="giftCardAmount">
          <el-input-number
            v-model="assignCustomerForm.giftCardAmount"
            :min="0"
            :precision="2"
            placeholder="请输入赠送金额"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAssignCustomerDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAssignCustomer">确认分配</el-button>
      </template>
    </el-dialog>



    <!-- 充值对话框 -->
    <el-dialog
      v-model="showRechargeDialog"
      title="批量充值"
      width="500px"
      @close="resetRechargeForm"
    >
      <el-form :model="rechargeForm" label-width="100px">
        <el-form-item label="充值金额:">
          <el-input-number
            v-model="rechargeForm.amount"
            :min="0.01"
            :precision="2"
            placeholder="请输入充值金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="充值说明:">
          <el-input
            v-model="rechargeForm.remark"
            type="textarea"
            rows="3"
            placeholder="请输入充值说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRechargeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchRecharge">确认充值</el-button>
      </template>
    </el-dialog>

    <!-- 单用户充值对话框 -->
    <el-dialog
      v-model="showSingleRechargeDialog"
      title="用户充值"
      width="500px"
      @close="resetRechargeForm"
    >
      <el-form :model="rechargeForm" label-width="100px">
        <el-form-item label="用户名称:">
          <span>{{ selectedUser?.name }}</span>
        </el-form-item>
        <el-form-item label="当前余额:">
          <span class="balance">¥{{ selectedUser?.balance.toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="充值金额:">
          <el-input-number
            v-model="rechargeForm.amount"
            :min="0.01"
            :precision="2"
            placeholder="请输入充值金额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="充值说明:">
          <el-input
            v-model="rechargeForm.remark"
            type="textarea"
            rows="3"
            placeholder="请输入充值说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSingleRechargeDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmSingleRecharge">确认充值</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Folder,
  Search,
  Money,
  Lock,
  Unlock,
  Document,
  Edit,
  Delete,
  Close
} from '@element-plus/icons-vue'
import {
  listInfo,
  addInfo,
  updateInfo,
  delInfo,
  addRelation
} from '@/api/system/customer'
import { listUser } from '@/api/system/appUser'

// 响应式数据
const searchKeyword = ref('')
const selectedUser = ref(null)
const selectedUsers = ref([])
const selectedGroupId = ref(null)
const showRechargeDialog = ref(false)
const showSingleRechargeDialog = ref(false)
const showAddGroupDialog = ref(false)
const showEditGroupDialog = ref(false)

const showAssignCustomerDialog = ref(false) // 分配客户对话框
const loading = ref(false) // 加载状态
const currentEditGroupId = ref(null) // 当前编辑的客户ID

// 表单引用
const groupFormRef = ref(null)

const assignCustomerFormRef = ref(null) // 分配客户表单引用
const groupTreeRef = ref(null) // 客户树组件引用

// 充值表单
const rechargeForm = reactive({
  amount: 0,
  remark: ''
})

// 新增客户表单
const groupForm = reactive({
  name: '',
  description: ''
})



// 分配客户表单
const assignCustomerForm = reactive({
  userId: null,
  userName: '',
  userPhone: '',
  customerId: null,
  giftCardAmount: 0,
  // remark: ''
})

// 表单验证规则
const groupRules = {
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
}



// 树结构配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 数据
const groupTreeData = ref([])
const allUsers = ref([])

// API调用函数
const loadCustomerList = async () => {
  try {
    loading.value = true
    const response = await listInfo()
    if (response.code === 200) {
      groupTreeData.value = response.rows.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        level: 1,
        userCount: 0 // 初始为0，后续通过用户关联数据计算
      }))
      await loadUserList() // 加载用户列表数据
    } else {
      ElMessage.error(response.msg || '获取客户列表失败')
    }
  } catch (error) {
    console.error('加载客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
  } finally {
    loading.value = false
  }
}

const loadUserList = async () => {
  try {
    const response = await listUser()
    if (response.code === 200) {
      allUsers.value = response.rows.map(item => ({
        id: item.id,
        openid: item.openid,
        unionid: item.unionid,
        name: item.nickname,
        avatar: item.avatarUrl,
        phone: item.phone,
        gender: item.gender, // 0未知，1男，2女
        birthday: item.birthday,
        balance: item.balance || 0,
        points: item.points || 0,
        memberLevel: item.memberLevel, // 0普通会员，1黄金会员，2白金会员等
        status: item.status === 1 ? 'active' : 'disabled', // 1启用，0禁用
        registerTime: item.registerTime,
        lastLoginTime: item.lastLoginTime || '--',
        source: item.source,
        isDeleted: item.isDeleted,
        remark: item.remark,
        coupon: item.coupon,
        groupId: null, // 暂时设为null，后续通过关联表获取
        groupName: null
      }))
      updateGroupUserCount() // 更新客户用户数量
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 计算属性
const filteredUserList = computed(() => {
  let users = allUsers.value.map(user => ({
    ...user,
    groupName: getGroupNameById(user.groupId)
  }))

  // 根据选中的客户过滤（如果有选中客户）
  if (selectedGroupId.value) {
    users = users.filter(user => user.groupId === selectedGroupId.value)
  }

  // 根据搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    users = users.filter(user => 
      user.name.toLowerCase().includes(keyword) || 
      user.phone.includes(keyword) ||
      (user.groupName && user.groupName.toLowerCase().includes(keyword))
    )
  }

  return users
})

// 获取客户名称的辅助函数
const getGroupNameById = (groupId) => {
  const group = groupTreeData.value.find(g => g.id === groupId)
  return group ? group.name : null
}

const currentGroupStats = computed(() => {
  if (!selectedGroupId.value) return null
  
  const groupUsers = allUsers.value.filter(user => user.groupId === selectedGroupId.value)
  
  return {
    totalUsers: groupUsers.length,
    activeUsers: groupUsers.filter(user => user.status === 'active').length,
    disabledUsers: groupUsers.filter(user => user.status === 'disabled').length,
    totalBalance: groupUsers.reduce((sum, user) => sum + user.balance, 0)
  }
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

const paginatedUserList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUserList.value.slice(start, end)
})

// 方法
const handleGroupSelect = (data) => {
  selectedGroupId.value = data.id
  selectedUser.value = null
  selectedUsers.value = []
  currentPage.value = 1 // 切换客户时重置分页
}

const handleUserSelection = (selection) => {
  selectedUsers.value = selection
}

const handleUserClick = (row) => {
  selectedUser.value = row
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleSizeChange = (val) => {
  pageSize.value = val
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}


const assignCustomer = (user) => {
  assignCustomerForm.userId = user.id
  assignCustomerForm.userName = user.name
  assignCustomerForm.userPhone = user.phone
  assignCustomerForm.customerId = null
  assignCustomerForm.giftCardAmount = 0
  // assignCustomerForm.remark = ''
  showAssignCustomerDialog.value = true
}



const batchToggleStatus = async (status) => {
  const action = status === 'active' ? '启用' : '禁用'
  try {
    await ElMessageBox.confirm(
      `确定要${action}选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量操作确认',
      { type: 'warning' }
    )
    
    selectedUsers.value.forEach(user => {
      user.status = status
    })
    
    ElMessage.success(`批量${action}成功`)
    selectedUsers.value = []
  } catch {
    // 用户取消操作
  }
}

const resetRechargeForm = () => {
  rechargeForm.amount = 0
  rechargeForm.remark = ''
}

const confirmBatchRecharge = async () => {
  if (rechargeForm.amount <= 0) {
    ElMessage.error('请输入有效的充值金额')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要为选中的 ${selectedUsers.value.length} 个用户充值 ¥${rechargeForm.amount.toFixed(2)} 吗？`,
      '批量充值确认',
      { type: 'warning' }
    )
    
    selectedUsers.value.forEach(user => {
      user.balance += rechargeForm.amount
    })
    
    ElMessage.success(`批量充值成功，共充值 ¥${(rechargeForm.amount * selectedUsers.value.length).toFixed(2)}`)
    showRechargeDialog.value = false
    resetRechargeForm()
    selectedUsers.value = []
  } catch {
    // 用户取消操作
  }
}

const confirmSingleRecharge = async () => {
  if (rechargeForm.amount <= 0) {
    ElMessage.error('请输入有效的充值金额')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要为用户 ${selectedUser.value.name} 充值 ¥${rechargeForm.amount.toFixed(2)} 吗？`,
      '充值确认',
      { type: 'warning' }
    )
    
    selectedUser.value.balance += rechargeForm.amount
    
    // 更新用户列表中的数据
    const userIndex = allUsers.value.findIndex(user => user.id === selectedUser.value.id)
    if (userIndex !== -1) {
      allUsers.value[userIndex].balance = selectedUser.value.balance
    }
    
    ElMessage.success(`充值成功，当前余额: ¥${selectedUser.value.balance.toFixed(2)}`)
    showSingleRechargeDialog.value = false
    resetRechargeForm()
  } catch {
    // 用户取消操作
  }
}

const viewUserHistory = () => {
  ElMessage.info(`查看用户 ${selectedUser.value.name} 的历史记录`)
}

const resetGroupForm = () => {
  groupForm.name = ''
  groupForm.description = ''
  if (groupFormRef.value) {
    groupFormRef.value.clearValidate()
  }
}

const confirmAddGroup = async () => {
  if (!groupFormRef.value) return

  try {
    await groupFormRef.value.validate()

    await ElMessageBox.confirm('确定要新增客户吗？', '确认操作', {
      type: 'warning'
    })

    const response = await addInfo({
      name: groupForm.name,
      description: groupForm.description
    })

    if (response.code === 200) {
      ElMessage.success('客户新增成功！')
      showAddGroupDialog.value = false
      resetGroupForm()
      await loadCustomerList() // 重新加载客户列表
    } else {
      ElMessage.error(response.msg || '新增客户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('新增客户失败:', error)
      ElMessage.error('新增客户失败')
    }
  }
}

const editGroup = (data) => {
  groupForm.name = data.name
  groupForm.description = data.description || ''
  currentEditGroupId.value = data.id
  showEditGroupDialog.value = true
}

const deleteGroup = async (data) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${data.name}" 吗？此操作将删除该客户下的所有用户关联。`,
      '确认操作',
      { type: 'warning' }
    )

    const response = await delInfo(data.id)

    if (response.code === 200) {
      // 清空选中状态
      if (selectedGroupId.value === data.id) {
        selectedGroupId.value = null
        selectedUser.value = null
        selectedUsers.value = []
      }

      ElMessage.success('客户删除成功！')
      await loadCustomerList() // 重新加载客户列表
    } else {
      ElMessage.error(response.msg || '删除客户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除客户失败:', error)
      ElMessage.error('删除客户失败')
    }
  }
}

const confirmEditGroup = async () => {
  if (!groupFormRef.value) return

  try {
    await groupFormRef.value.validate()

    await ElMessageBox.confirm('确定要修改客户吗？', '确认操作', {
      type: 'warning'
    })

    const response = await updateInfo({
      id: currentEditGroupId.value,
      name: groupForm.name,
      description: groupForm.description
    })

    if (response.code === 200) {
      ElMessage.success('客户修改成功！')
      showEditGroupDialog.value = false
      resetGroupForm()
      await loadCustomerList() // 重新加载客户列表
    } else {
      ElMessage.error(response.msg || '修改客户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改客户失败:', error)
      ElMessage.error('修改客户失败')
    }
  }
}





const updateGroupUserCount = () => {
  groupTreeData.value.forEach(group => {
    const count = allUsers.value.filter(user => user.groupId === group.id).length
    group.userCount = count
  })
}

const confirmAssignCustomer = async () => {
  if (!assignCustomerFormRef.value) return

  try {
    await assignCustomerFormRef.value.validate()

    await ElMessageBox.confirm('确定要分配客户吗？', '确认操作', {
      type: 'warning'
    })

    const response = await addRelation({
      customerId: assignCustomerForm.customerId,
      userId: assignCustomerForm.userId,
      giftCardAmount: assignCustomerForm.giftCardAmount,
      // remark: assignCustomerForm.remark
    })

    if (response.code === 200) {
      ElMessage.success('客户分配成功！')
      showAssignCustomerDialog.value = false
      resetAssignCustomerForm()
      await loadUserList() // 重新加载用户列表数据
      await loadCustomerList() // 重新加载客户列表数据
    } else {
      ElMessage.error(response.msg || '分配客户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('分配客户失败:', error)
      ElMessage.error('分配客户失败')
    }
  }
}

const resetAssignCustomerForm = () => {
  assignCustomerForm.userId = null
  assignCustomerForm.userName = ''
  assignCustomerForm.userPhone = ''
  assignCustomerForm.customerId = null
  assignCustomerForm.giftCardAmount = 0
  assignCustomerForm.remark = ''
  if (assignCustomerFormRef.value) {
    assignCustomerFormRef.value.clearValidate()
  }
}

const assignCustomerRules = {
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  giftCardAmount: [{ required: true, message: '请输入赠送金额', trigger: 'blur' }, { type: 'number', min: 0, message: '金额必须大于等于0', trigger: 'blur' }]
}

const clearGroupSelection = () => {
  selectedGroupId.value = null
  selectedUser.value = null
  selectedUsers.value = []
  currentPage.value = 1
  // 清除树组件的选中状态
  if (groupTreeRef.value) {
    groupTreeRef.value.setCurrentKey(null)
  }
}

onMounted(() => {
  // 初始化时不选中任何客户，显示所有用户
  selectedGroupId.value = null
  // 加载初始数据
  loadCustomerList()
})
</script>

<style scoped lang="scss">
.group-admin-container {
  height: 91vh;
  background: #f5f5f5;
  
  .layout-wrapper {
    display: flex;
    height: 100%;
    gap: 16px;
    padding: 16px;
    overflow: auto;
  }
  
  .left-panel {
    width: 280px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    
    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }
    
    .tree-container {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      
      .tree-node {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: space-between;
        width: 100%;
        
        .node-content {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
        }
        
        .node-label {
          flex: 1;
        }
        
        .user-count {
          color: #909399;
          font-size: 12px;
        }

        .node-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.2s;
          
          .el-button {
            padding: 4px;
            margin: 0;
            
            &.danger-text {
              color: #f56c6c;
              
              &:hover {
                color: #f78989;
              }
            }
          }
        }
        
        &:hover .node-actions {
          opacity: 1;
        }
      }
    }
  }
  
  .middle-panel {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: auto;
    
    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        align-items: center;
      }
    }
    
    .user-list-container {
      flex: 1;
      padding: 16px;
      display: flex;
      flex-direction: column;
      
      .balance {
        color: #f56c6c;
        font-weight: bold;
      }
      
      .danger-text {
        color: #f56c6c;
      }
      
      .success-text {
        color: #67c23a;
      }
      
      .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px 0;
        border-top: 1px solid #ebeef5;
        margin-top: 16px;
      }
    }
  }
  
  .right-panel {
    width: 320px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    
    .panel-header {
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }
    
    .management-content {
      flex: 1;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .selected-user-info {
        .user-info-header {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .user-details {
            flex: 1;
            
            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              color: #303133;
            }
            
            p {
              margin: 2px 0;
              font-size: 12px;
              color: #909399;
            }
            
            .balance {
              color: #f56c6c;
              font-weight: bold;
            }
          }
        }
      }
      
      .batch-operations,
      .single-operations {
        .batch-actions,
        .single-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
          .el-button{
            margin-left: 0 !important;
          }
        }
        .batch-header {
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
          font-size: 15px;
      
        }
        .single-header {
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
          font-size: 15px;
        }
      }
      
      .statistics {
        margin-top: auto;
        
        .stats-content {
          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f5f7fa;
            
            &:last-child {
              border-bottom: none;
            }
            
            .label {
              color: #606266;
              font-size: 14px;
            }
            
            .value {
              font-weight: bold;
              
              &.success {
                color: #67c23a;
              }
              
              &.danger {
                color: #f56c6c;
              }
              
              &.balance {
                color: #e6a23c;
              }
            }
          }
        }
      }
    }
  }
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

:deep(.el-card__body) {
  padding: 16px;
}
:deep(.el-table__row) {
  cursor: pointer;
}
</style>