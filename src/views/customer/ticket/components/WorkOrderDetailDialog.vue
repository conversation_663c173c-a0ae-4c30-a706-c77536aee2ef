<template>
  <el-dialog
    v-model="visible"
    title="平台工单详情"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 回复处理记录链接 -->
    <div class="reply-record-link">
      <el-link type="primary" @click="showReplyRecord = !showReplyRecord">
        <el-icon><Document /></el-icon>
        回复处理记录
      </el-link>
    </div>

    <!-- 回复历史记录 -->
    <div v-if="showReplyRecord" class="reply-history">
      <div class="history-item" v-for="(item, index) in replyHistory" :key="index">
        <div class="history-content" :class="{ 'right': item.isRight }">
          <div class="message-info">
            <span class="sender">{{ item.sender }}</span>
            <span class="timestamp">{{ item.timestamp }}</span>
          </div>
          <div class="message-bubble">
            <p v-if="item.message">{{ item.message }}</p>
            <div v-if="item.attachments && item.attachments.length > 0" class="attachments">
              <div 
                v-for="(attachment, idx) in item.attachments" 
                :key="idx" 
                class="attachment-item"
                @click="previewImage(attachment)"
              >
                <el-image
                  :src="attachment.url"
                  :preview-src-list="[attachment.url]"
                  fit="cover"
                  class="attachment-image"
                />
              </div>
            </div>
          </div>
          <div v-if="item.actions" class="message-actions">
            <el-button 
              v-for="action in item.actions" 
              :key="action.label"
              :type="action.type || 'default'"
              size="small"
            >
              {{ action.label }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单信息表单 -->
    <el-form :model="workOrderForm" :rules="rules" ref="formRef" label-width="120px" class="work-order-form">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="工单Id">
            <span>{{ workOrderForm.workOrderId }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单号">
            <el-button 
              type="primary" 
              link 
              @click="handleViewOrderDetail"
            >
              {{ workOrderForm.orderNumber }}
            </el-button>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="品牌">
            <span>{{ workOrderForm.brand }}</span>
          </el-form-item>
        </el-col> -->
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="业务类型" prop="businessType" required>
            <el-select v-model="workOrderForm.businessType" placeholder="请选择" style="width: 100%" @change="handleBusinessTypeChange">
              <el-option label="客诉" value="complaint" />
              <el-option label="售前咨询" value="presales" />
              <el-option label="售后咨询" value="aftersales" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="子业务类型" prop="businessSubType">
            <el-select v-model="workOrderForm.businessSubType" placeholder="请选择" style="width: 100%" :disabled="!workOrderForm.businessType">
              <el-option 
                v-for="option in businessSubTypeOptions" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="工厂需介入工单">
            <el-switch v-model="workOrderForm.factoryIntervention" />
          </el-form-item>
        </el-col> -->
      </el-row>

      <el-form-item label="内容" prop="content" required>
        <el-input
          v-model="workOrderForm.content"
          type="textarea"
          :rows="4"
          placeholder="请输入回复内容"
        />
      </el-form-item>

      <el-form-item label="附件上传">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          list-type="picture-card"
          :limit="5"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              支持jpg/png文件，且不超过2MB
            </div>
          </template>
        </el-upload>
        
        <!-- 剪贴板上传按钮 -->
        <el-button type="primary" @click="pasteFromClipboard" style="margin-top: 10px;">
          <el-icon><DocumentCopy /></el-icon>
          剪切板
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="default" @click="handleEnd">结束</el-button>
        <el-button type="primary" @click="handleAddComment">追加留言</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 订单详情弹窗 -->
  <OrderDetailDialog
    v-model="orderDetailVisible"
    :order-data="currentOrderData"
  />
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { Document, Plus, DocumentCopy } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import OrderDetailDialog from '../../listTicket/components/OrderDetailDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  workOrderData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit']);

// 响应式数据
const visible = ref(false);
const showReplyRecord = ref(true);
const formRef = ref();
const uploadRef = ref();
const fileList = ref([]);

// 订单详情相关
const orderDetailVisible = ref(false);
const currentOrderData = ref({});

// 工单表单数据
const workOrderForm = reactive({
  workOrderId: '',
  orderNumber: '',
  brand: '',
  businessType: '',
  businessSubType: '',
  factoryIntervention: false,
  content: ''
});

// 业务类型子选项映射
const businessTypeMap = {
  complaint: [
    { label: '升级客诉', value: 'escalated' },
    { label: '差评删除/删差评得分核销', value: 'review_delete' },
    { label: '升级客诉/美团差评', value: 'meituan' },
    { label: '普通客诉', value: 'general' }
  ],
  presales: [
    { label: '业务咨询/衣物洗护', value: 'consultation' }
  ],
  aftersales: [
    { label: '订单查询', value: 'order_query' }
  ]
};

// 当前可选的子业务类型选项
const businessSubTypeOptions = ref([]);

// 表单验证规则
const rules = {
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' }
  ]
};

// 回复历史数据
const replyHistory = ref([
  {
    timestamp: '2025-08-21 08:56:05',
    sender: '鲸峰云:售前-程自欣',
    message: '关联1682544,客户催促今天联系处理,辛苦尽快致电客户售后',
    isRight: false,
    attachments: [
      {
        url: 'https://ww2.sinaimg.cn/mw690/007ut4Uhly1hx4v37mpxcj30u017cgrv.jpg',
        name: 'attachment1.jpg'
      }
    ]
  },
  {
    timestamp: '2025-08-21 09:04:05',
    sender: '圣洛依--何群',
    message: '收到',
    isRight: true,
    // actions: [
    //   { label: '收到', type: 'primary' }
    // ]
  }
]);

// 上传配置
const uploadAction = '/api/upload'; // 替换为实际的上传接口
const uploadHeaders = {
  // 添加认证头等
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal && props.workOrderData) {
    initFormData();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
});

// 处理业务类型变化
const handleBusinessTypeChange = (value) => {
  workOrderForm.businessSubType = ''; // 清空子类型选择
  if (value && businessTypeMap[value]) {
    businessSubTypeOptions.value = businessTypeMap[value];
  } else {
    businessSubTypeOptions.value = [];
  }
};

// 初始化表单数据
const initFormData = () => {
  const data = props.workOrderData;
  workOrderForm.workOrderId = data.workOrderId || '';
  workOrderForm.orderNumber = data.orderNumber || '';
  workOrderForm.brand = data.brand || '大鲸洗';
  workOrderForm.businessType = data.businessType || '';
  workOrderForm.businessSubType = data.businessSubType || '';
  workOrderForm.factoryIntervention = data.factoryIntervention || false;
  workOrderForm.content = data.content || '';
  fileList.value = data.attachments || [];
  
  // 初始化子业务类型选项
  if (workOrderForm.businessType && businessTypeMap[workOrderForm.businessType]) {
    businessSubTypeOptions.value = businessTypeMap[workOrderForm.businessType];
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 预览图片
const previewImage = (attachment) => {
  // 使用Element Plus的图片预览功能
  console.log('预览图片:', attachment);
};

// 上传成功回调
const handleUploadSuccess = (response, file, fileList) => {
  ElMessage.success('上传成功');
  console.log('上传成功:', response);
};

// 上传失败回调
const handleUploadError = (error, file, fileList) => {
  ElMessage.error('上传失败');
  console.error('上传失败:', error);
};

// 移除文件
const handleFileRemove = (file, fileList) => {
  console.log('移除文件:', file);
};

// 上传前验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 从剪贴板粘贴
const pasteFromClipboard = async () => {
    try {
        const clipboardContent = await navigator.clipboard.read();
        const clipboardItem = clipboardContent[0];
        let noImg = true;
        for (const type of clipboardItem.types) {
            if (type === "image/png") {
                noImg = false;

                const blob = await clipboardItem.getType(type);
                const url = URL.createObjectURL(blob);

                // 如果需要 File 对象
                const file = new File([blob], "clipboard-image.png", { type });
                console.log(file);

                const img = document.createElement("img");
                img.src = url;
                img.width = 300;
                img.onload = () => {
                    URL.revokeObjectURL(img.src);
                };
                // refPasteBox.value?.appendChild(img);
                fileList.value.push(file);
            }
        }
        if (noImg) {
            alert("当前剪切板中没有图片。\n Windows 系统可通过快捷键\n ⌘+V \n查看剪切板");
        }
    } catch (err) {
        if (err.name === "NotAllowedError") {
            console.log("用户拒绝了访问剪贴板");
        } else {
            console.error("无法读取剪贴板内容: ", err);
        }
    }
};

// 结束工单
const handleEnd = () => {
  ElMessage.info('工单已结束');
  handleClose();
};

// 查看订单详情
const handleViewOrderDetail = () => {
  // 模拟订单数据
  currentOrderData.value = {
    traceNumber: workOrderForm.orderNumber,
    orderStatus: '处理中',
    orderSource: '线上订单',
    createTime: '2025-08-21 10:30:00',
    payTime: '2025-08-21 10:35:00',
    appointmentTime: '2025-08-22 14:00:00',
    deliveryTime: '2025-08-23 16:00:00',
    factoryTime: '2025-08-22 09:00:00',
    merchantRemark: '客户要求尽快处理',
    userPhone: '138****8888',
    isVip: true,
    pickupAddress: '北京市朝阳区xxx街道xxx小区',
    deliveryAddress: '北京市朝阳区xxx街道xxx小区',
    userRemark: '请轻拿轻放',
    clothingCount: 5,
    actualCount: 5,
    clothingList: [
      {
        name: '羽绒服',
        washCode: '101010620322',
        modelNumber: 'YRF001',
        memo: '无',
        defect: '无',
        status: '已洗涤',
        beforeWash: '干洗',
        afterWash: '干洗'
      },
      {
        name: '牛仔裤',
        washCode: '101010620346',
        modelNumber: 'NZK002',
        memo: '无',
        defect: '无',
        status: '已洗涤',
        beforeWash: '水洗',
        afterWash: '水洗'
      }
    ],
    productList: [
      {
        productName: '干洗服务',
        quantity: 3,
        unitPrice: 15.00,
        totalPrice: 45.00
      },
      {
        productName: '水洗服务',
        quantity: 2,
        unitPrice: 10.00,
        totalPrice: 20.00
      }
    ],
    productTotal: 65.00,
    discount: 5.00,
    orderTotal: 60.00,
    coupon: 10.00,
    cashPayment: 50.00,
    payableCash: 50.00,
    actualPayment: 50.00,
    netAmount: 50.00,
    pickupLogisticsNumber: 'SF1234567890',
    deliveryLogisticsNumber: 'SF0987654321',
    pickupTimeline: [
      {
        id: 1,
        time: '2025-08-21 14:30:00',
        content: '快递员已取件'
      },
      {
        id: 2,
        time: '2025-08-21 16:00:00',
        content: '已到达分拣中心'
      }
    ],
    deliveryTimeline: [
      {
        id: 1,
        time: '2025-08-23 09:00:00',
        content: '开始配送'
      },
      {
        id: 2,
        time: '2025-08-23 16:00:00',
        content: '已送达'
      }
    ],
    orderTickets: []
  };
  
  orderDetailVisible.value = true;
};

// 追加留言
const handleAddComment = async () => {
  try {
    await formRef.value.validate();
    
    const formData = {
      ...workOrderForm,
      attachments: fileList.value
    };
    
    emit('submit', formData);
    ElMessage.success('留言已提交');
    handleClose();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<style scoped>
.reply-record-link {
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.reply-history {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.history-item {
  margin-bottom: 20px;
}

.history-content {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.history-content.right {
  align-items: flex-end;
  margin-left: auto;
}

.message-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.history-content.right .message-info {
  flex-direction: row-reverse;
}

.message-info .sender {
  font-weight: 500;
  margin-right: 8px;
}

.history-content.right .message-info .sender {
  margin-right: 0;
  margin-left: 8px;
}

.message-info .timestamp {
  color: #999;
}

.message-bubble {
  background-color: #fff;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  border: 1px solid #e8e8e8;
}

.history-content.right .message-bubble {
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

.attachments {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ddd;
}

.attachment-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
}

.message-actions {
  margin-top: 8px;
}

.work-order-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style> 