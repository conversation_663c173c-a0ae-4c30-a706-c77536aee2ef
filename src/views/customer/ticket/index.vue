<template>
  <div class="app-container">
    <!-- 工单详情弹窗 -->
    <WorkOrderDetailDialog
      v-model:visible="dialogVisible"
      :work-order-data="currentWorkOrder"
      @submit="handleWorkOrderSubmit"
    />
    <el-row>
      <el-col :span="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="100px">
          <el-form-item label="订单号" prop="orderNumber">
            <el-input v-model="queryParams.orderNumber" placeholder="请输入订单号" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="平台工单ID" prop="workOrderId">
            <el-input v-model="queryParams.workOrderId" placeholder="请输入平台工单ID" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 160px">
              <el-option label="全部" value="" />
              <el-option label="平台已回复" value="platform_replied" />
              <el-option label="工厂已回复" value="factory_replied" />
              <el-option label="结束" value="ended" />
            </el-select>
          </el-form-item>
          <el-form-item label="回复超时" prop="replyTimeout">
            <el-select v-model="queryParams.replyTimeout" placeholder="请选择" clearable style="width: 160px">
              <el-option label="超24小时" value="24" />
              <el-option label="超48小时" value="48" />
            </el-select>
          </el-form-item>
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="queryParams.businessType" placeholder="请选择" clearable style="width: 160px" @change="handleBusinessTypeChange">
              <el-option label="客诉" value="complaint" />
              <el-option label="售前咨询" value="presales" />
              <el-option label="售后咨询" value="aftersales" />
            </el-select>
            <el-select v-model="queryParams.businessSubType" placeholder="请选择" clearable style="width: 160px" :disabled="!queryParams.businessType">
              <el-option 
                v-for="option in businessSubTypeOptions" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="回复时间">
            <el-date-picker 
              v-model="queryParams.replyTimeRange" 
              type="datetimerange" 
              range-separator="-"
              start-placeholder="请选择开始时间" 
              end-placeholder="请选择结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 360px">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发起时间">
            <el-date-picker 
              v-model="queryParams.initiationTimeRange" 
              type="datetimerange" 
              range-separator="-"
              start-placeholder="请选择开始时间" 
              end-placeholder="请选择结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 360px">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="关单时间">
            <el-date-picker 
              v-model="queryParams.closeTimeRange" 
              type="datetimerange" 
              range-separator="-"
              start-placeholder="请选择开始时间" 
              end-placeholder="请选择结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 360px">
            </el-date-picker>
            <el-badge :value="0" class="item" style="margin-left: 10px;">
              <el-icon><Bell /></el-icon>
            </el-badge>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-table :data="tableData" border stripe style="width: 100%" @row-click="handleRowClick">
          <el-table-column prop="operation" label="操作" width="100" align="center">
            <template #default="scope">
              <el-link type="primary" @click="handleProcess(scope.row)">处理</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="workOrderId" label="平台工单ID" width="200" align="center"/>
          <el-table-column prop="businessType" label="业务类型" width="300" align="center"/>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="initiationTime" label="发起时间" width="180" align="center"/>
          <el-table-column prop="factoryReplyTime" label="工厂回复时间" width="180" align="center"/>
          <el-table-column prop="factoryReplyDuration" label="工厂回复时长(分钟)" width="150" align="center"/>
          <el-table-column prop="orderNumber" label="订单号" width="180" align="center"/>
        </el-table>
        <div style="display: flex; justify-content: center; margin-top: 20px;">
          <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs } from 'vue';
import { Bell } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue';

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 查询参数
const queryParams = reactive({
  orderNumber: '',
  workOrderId: '',
  status: '',
  replyTimeout: '',
  businessType: '',
  businessSubType: '',
  replyTimeRange: [],
  initiationTimeRange: [],
  closeTimeRange: []
});

// 业务类型子选项映射
const businessTypeMap = {
  complaint: [
    { label: '升级客诉', value: 'escalated' },
    { label: '差评删除/删差评得分核销', value: 'review_delete' },
    { label: '升级客诉/美团差评', value: 'meituan' }
  ],
  presales: [
    { label: '业务咨询/衣物洗护', value: 'consultation' }
  ],
  aftersales: [
    { label: '订单查询', value: 'order_query' }
  ]
};

// 当前可选的子业务类型选项
const businessSubTypeOptions = ref([]);

// 弹窗相关数据
const dialogVisible = ref(false);
const currentWorkOrder = ref({});

// 表格数据
const tableData = ref([
  {
    id: 1,
    workOrderId: 'CL17557584127214798',
    businessType: '客诉-差评删除/删差评得分核销',
    status: '结束',
    initiationTime: '2025-08-21 14:40:12',
    factoryReplyTime: '2025-08-21 11:20:59',
    factoryReplyDuration: 5,
    orderNumber: 'CM095643137132242'
  },
  {
    id: 2,
    workOrderId: 'DD175574261817734664',
    businessType: '售前咨询-业务咨询/衣物洗护',
    status: '工厂已回复',
    initiationTime: '2025-08-21 10:30:15',
    factoryReplyTime: '2025-08-21 10:35:20',
    factoryReplyDuration: 8,
    orderNumber: 'DM096340295132354'
  },
  {
    id: 3,
    workOrderId: 'CL17557584127214799',
    businessType: '客诉-升级客诉/美团差评',
    status: '结束',
    initiationTime: '2025-08-20 09:15:30',
    factoryReplyTime: '2025-08-20 23:30:45',
    factoryReplyDuration: 874,
    orderNumber: 'CM095643137132243'
  },
  {
    id: 4,
    workOrderId: 'DD175574261817734665',
    businessType: '售后咨询-订单查询',
    status: '工厂已回复',
    initiationTime: '2025-08-19 16:20:10',
    factoryReplyTime: '2025-08-20 09:05:15',
    factoryReplyDuration: 1005,
    orderNumber: 'DM096340295132355'
  },
  {
    id: 5,
    workOrderId: 'CL17557584127214800',
    businessType: '客诉—升级客诉',
    status: '平台已回复',
    initiationTime: '2025-08-18 11:45:22',
    factoryReplyTime: '',
    factoryReplyDuration: '',
    orderNumber: 'CM095643137132244'
  }
]);

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case '结束':
      return 'success';
    case '工厂已回复':
      return 'warning';
    case '平台已回复':
      return 'info';
    default:
      return '';
  }
};

// 获取完整的业务类型显示文本
const getFullBusinessType = (mainType, subType) => {
  if (!mainType) return '';
  const mainTypeLabel = {
    'complaint': '客诉',
    'presales': '售前咨询',
    'aftersales': '售后咨询'
  }[mainType] || mainType;
  
  if (!subType) return mainTypeLabel;
  
  const subTypeLabel = businessTypeMap[mainType]?.find(item => item.value === subType)?.label || subType;
  return `${mainTypeLabel}-${subTypeLabel}`;
};

// 查询方法
const handleQuery = () => {
  const fullBusinessType = getFullBusinessType(queryParams.businessType, queryParams.businessSubType);
  console.log('查询参数:', {
    ...queryParams,
    fullBusinessType
  });
  // 这里添加实际的查询逻辑
};

// 处理业务类型变化
const handleBusinessTypeChange = (value) => {
  queryParams.businessSubType = ''; // 清空子类型选择
  if (value && businessTypeMap[value]) {
    businessSubTypeOptions.value = businessTypeMap[value];
  } else {
    businessSubTypeOptions.value = [];
  }
};

// 重置查询
const resetQuery = () => {
  Object.keys(queryParams).forEach(key => {
    if (Array.isArray(queryParams[key])) {
      queryParams[key] = [];
    } else {
      queryParams[key] = '';
    }
  });
  businessSubTypeOptions.value = []; // 重置子类型选项
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('点击行:', row);
};

// 处理操作
const handleProcess = (row) => {
  console.log('处理工单:', row);
  // 解析业务类型
  const businessTypeParts = row.businessType.split('-');
  const mainType = businessTypeParts[0];
  const subType = businessTypeParts[1] || '';
  
  // 映射业务类型
  const typeMapping = {
    '客诉': 'complaint',
    '售前咨询': 'presales',
    '售后咨询': 'aftersales'
  };
  
  currentWorkOrder.value = {
    workOrderId: row.workOrderId,
    orderNumber: row.orderNumber,
    brand: '大鲸洗',
    businessType: typeMapping[mainType] || mainType,
    businessSubType: subType,
    factoryIntervention: false,
    content: '',
    attachments: []
  };
  dialogVisible.value = true;
};

// 处理工单提交
const handleWorkOrderSubmit = (formData) => {
  console.log('工单提交数据:', formData);
  // 这里添加实际的提交逻辑
  ElMessage.success('工单处理成功');
};

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.pageSize = val;
  // 重新加载数据
};

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  // 重新加载数据
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-table {
  margin-top: 20px;
}

.item {
  margin-top: 10px;
  margin-right: 40px;
}
</style>