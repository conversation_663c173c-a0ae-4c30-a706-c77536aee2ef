<template>
  <div class="work-order">
    <!-- 查询表单 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="创建时间:">
            <el-date-picker
              v-model="searchForm.createTime"
              type="datetimerange"
              range-separator="到"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          
          <el-form-item label="工单ID:">
            <el-input 
              v-model="searchForm.workOrderId" 
              placeholder="请输入工单ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="水洗码:">
            <el-input 
              v-model="searchForm.washCode" 
              placeholder="请输入水洗码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="订单号:">
            <el-input 
              v-model="searchForm.orderNumber" 
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <!-- <el-form-item label="品牌:">
            <el-select 
              v-model="searchForm.brand" 
              placeholder="全部"
              clearable
              style="width: 150px"
            >
              <el-option label="大鲸洗" value="1" />
            </el-select>
          </el-form-item> -->
          
          <el-form-item label="工单类型:">
            <el-select 
              v-model="searchForm.workOrderType" 
              placeholder="全部"
              clearable
              style="width: 150px"
            >
              <el-option 
                v-for="item in workOrderTypes"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        
        <div class="search-buttons">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
      
      <!-- 标签过滤 -->
      <div class="filter-tabs">
        <el-button 
          :type="activeTab === 'all' ? 'primary' : ''"
          :plain="activeTab !== 'all'"
          @click="setActiveTab('all')"
        >
          我的处理
        </el-button>
        <el-button 
          :type="activeTab === 'processing' ? 'primary' : ''"
          :plain="activeTab !== 'processing'"
          @click="setActiveTab('processing')"
        >
          处理中工单
        </el-button>
        <el-button 
          :type="activeTab === 'finished' ? 'primary' : ''"
          :plain="activeTab !== 'finished'"
          @click="setActiveTab('finished')"
        >
          已处理工单
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table 
        :data="tableData" 
        v-loading="loading"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="operation" label="操作" width="80" fixed="left">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="工单ID" width="100" />
        <el-table-column prop="orderNo" label="订单编号" width="180">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              @click="handleViewOrderDetail(scope.row)"
            >
              {{ scope.row.orderNo }}
            </el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="brand" label="品牌" width="80">
          <template #default="scope">
            大鲸洗
          </template>
        </el-table-column> -->
        <el-table-column prop="duration" label="当前状态持续时长" width="160" align="center">
          <template #default="scope">
            <el-tag :type="getTimeTagType(scope.row.duration)" size="small">
              {{ scope.row.duration }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priorityLevel" label="优先级" width="100" align="center" />
        <el-table-column prop="workOrderTitle" label="工单标题" min-width="150" align="center" />
        <el-table-column prop="amount" label="金额" width="80">
          <template #default="scope">
            处理中
          </template>
        </el-table-column>
        <el-table-column prop="workOrderStatus" label="工单状态" width="100">
          <template #default="scope">
            <el-tag :type="getWorkOrderStatusTagType(scope.row.workOrderStatus)" size="small">
              {{ getWorkOrderStatusName(scope.row.workOrderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderType" label="工单类型" width="120">
          <template #default="scope">
            {{ getWorkOrderTypeName(scope.row.workOrderType) }}
          </template>
        </el-table-column>
        <el-table-column prop="washCode" label="水洗码" width="180">
          <template #default="scope">
            北京朝阳洗衣工厂
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" align="center" width="100">
          <template #default="scope">
            {{ scope.row.createBy || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="currentHandler" label="当前处理人" align="center" width="120">
          <template #default="scope">
            {{ scope.row.currentHandler || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="createAt" label="创建时间" width="160" />
        <el-table-column prop="updateAt" label="最新更新时间" width="160" />
        <el-table-column prop="totalDuration" label="总持续时间" width="140">
          <template #default="scope">
           <strong> {{ scope.row.totalDuration }}</strong>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗组件 -->
    <OrderDetailDialog 
      v-model="orderDetailVisible" 
      :order-data="orderDetailData" 
    />
    
    <!-- 工单编辑弹窗组件 -->
    <WorkOrderEditDialog 
      v-model="workOrderEditVisible" 
      :work-order-id="currentWorkOrderId"
      @submit="handleWorkOrderSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { listInfo, getInfo, updateInfo } from '@/api/system/wokerOrder'
import { ElMessage } from 'element-plus'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import WorkOrderEditDialog from './components/WorkOrderEditDialog.vue'

// 搜索表单数据
const searchForm = reactive({
  createTime: '',
  workOrderId: '',
  washCode: '',
  orderNumber: '',
  brand: '',
  workOrderType: ''
})

// 当前激活的标签
const activeTab = ref('all')

// 工单类型数据
const workOrderTypes = ref([
  // { id: 1000, code: "QUANTITY_MISMATCH", name: "数量不符" },
  { id: 1, code: "WASH_AFTER_SALE", name: "洗涤售后" },
  // { id: 1002, code: "LOSE_LESS", name: "丢件少件" },
  // { id: 1003, code: "URGENT_ORDER", name: "加急单" },
  // { id: 1004, code: "NOTED_INSPECTION", name: "备注排查" },
  // { id: 1005, code: "EXPRESS_DUTY", name: "物流责任跟进" },
  // { id: 1006, code: "ORDER_TIMEOUT_WARNING", name: "订单超时预警" },
  // { id: 1007, code: "ORDEREDITING", name: "订单修改" },
  // { id: 1008, code: "CATEGORY_MISMATCH", name: "品类不符" },
  { id: 2, code: "MAJOR_RISK", name: "重大风险" },
  { id: 3, code: "PREMIUM_ADJUSTMENT", name: "大牌补价" },
  // { id: 1011, code: "EXPRESS_WARNING", name: "物流预警" },
  // { id: 1012, code: "COMPLAINT", name: "投诉" },
  // { id: 1013, code: "PERSONAL_BUSINESS", name: "个人任务" },
  // { id: 1014, code: "CHANNEL_REFUND", name: "渠道退款" },
  // { id: 1015, code: "SUPPLEMENT_REFUND", name: "补退款" }
])

// 表格数据
const tableData = ref([])

// 加载状态
const loading = ref(false)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 订单详情弹窗相关
const orderDetailVisible = ref(false)
const orderDetailData = ref({})

// 工单编辑弹窗相关
const workOrderEditVisible = ref(false)
const currentWorkOrderId = ref('')

// 获取工单列表数据
const getWorkOrderList = async () => {
  loading.value = true
  
  try {
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...searchForm,
      id: searchForm.workOrderId,
      orderNo: searchForm.orderNumber,
    }
    delete params.workOrderId
    delete params.orderNumber
    
    // 根据激活标签添加状态筛选
    if (activeTab.value === 'processing') {
      params.workOrderStatus = '2' // 待处理和处理中
    } else if (activeTab.value === 'finished') {
      params.workOrderStatus = '3' // 已完结
    }
    
    // 处理时间范围参数
    if (searchForm.createTime && searchForm.createTime.length === 2) {
      params.createAt = searchForm.createTime[0]
      params.updateAt = searchForm.createTime[1]
      delete params.createTime
    }
    
    const response = await listInfo(params)
    
    if (response.code === 200) {
      tableData.value = response.rows || []
      pagination.total = response.total || 0
    } else {
      ElMessage.error(response.msg || '获取工单列表失败')
    }
  } catch (error) {
    console.error('获取工单列表失败:', error)
    ElMessage.error('获取工单列表失败')
  } finally {
    loading.value = false
  }
}

// 设置激活标签
const setActiveTab = (tab) => {
  activeTab.value = tab
  pagination.currentPage = 1
  getWorkOrderList()
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  getWorkOrderList()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1
  getWorkOrderList()
}

// 编辑工单
const handleEdit = async (row) => {
  try {
    // 获取工单详细信息
    const response = await getInfo(row.id)
    
    if (response.code === 200) {
      currentWorkOrderId.value = row.id.toString()
      workOrderEditVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取工单详情失败')
    }
  } catch (error) {
    console.error('获取工单详情失败:', error)
    ElMessage.error('获取工单详情失败')
  }
}

// 查看订单详情
const handleViewOrderDetail = (row) => {
  // 模拟加载订单详情数据
  orderDetailData.value = {
    traceNumber: row.orderNo,
    orderStatus: '完毕',
    orderSource: '大鲸洗-美团',
    createTime: '2025-06-23 12:29:49',
    payTime: '2025-06-23 12:29:49',
    appointmentTime: '2025-06-23 14:00',
    deliveryTime: '',
    factoryTime: '',
    merchantRemark: '',
    userPhone: '18801072843',
    isVip: true,
    pickupAddress: '许, 18801072843, 北京市北京市朝阳区农光胡里1层-112号楼七楼平-401',
    deliveryAddress: '许, 18801072843, 北京市北京市朝阳区农光胡里1层-112号楼七楼平-401',
    userRemark: '',
    clothingCount: 5,
    actualCount: 5,
    clothingList: [
      {
        name: '休闲裤 (不反分项重申框面)',
        washCode: '101010620322',
        modelNumber: 'A0080',
        memo: '',
        defect: '',
        status: '已出厂',
        clothingImage: '查看',
        beforeWash: '否',
        afterWash: '否'
      },
      {
        name: '休闲裤 (不反分项重申框面)',
        washCode: '101010620346',
        modelNumber: 'A0080',
        memo: '',
        defect: '',
        status: '已出厂',
        clothingImage: '查看',
        beforeWash: '否',
        afterWash: '否'
      },
      {
        name: '羽绒服',
        washCode: '101010620360',
        modelNumber: 'A0080',
        memo: '',
        defect: '',
        status: '已出厂',
        clothingImage: '查看',
        beforeWash: '否',
        afterWash: '否'
      },
      {
        name: '普通衬衣',
        washCode: '101010620391',
        modelNumber: 'A0080',
        memo: '',
        defect: '',
        status: '已出厂',
        clothingImage: '查看',
        beforeWash: '否',
        afterWash: '否'
      },
      {
        name: '休闲裤/西裤',
        washCode: '101010620445',
        modelNumber: 'A0080',
        memo: '',
        defect: '',
        status: '已出厂',
        clothingImage: '查看',
        beforeWash: '否',
        afterWash: '否'
      }
    ],
    productList: [
      {
        productName: '精婉衣鞋洗涤/洗/双',
        quantity: 1,
        unitPrice: 120,
        totalPrice: 120
      }
    ],
    productTotal: 120,
    discount: 32,
    orderTotal: 88,
    coupon: 0,
    cashPayment: 88,
    payableCash: 88,
    actualPayment: 88,
    netAmount: 88,
    pickupLogisticsNumber: 'SF31895864322282',
    deliveryLogisticsNumber: 'SF31976090950934',
    pickupTimeline: [
      {
        id: 1,
        time: '2025-06-27 08:12:16',
        content: '在等网"运单异常解决中", 可查看该收入信息'
      },
      {
        id: 2,
        time: '2025-06-27 08:12:17',
        content: '您的快件已派送至本人，如有疑问请电联快派员【李元东, 电话：13237477300】，您的快递已于我们这关重要，如果您对我们的服务有任何的建议或意见，请联络联系我们，我们一定用心解听，全力改进，不辜负您的信任与支持。'
      },
      {
        id: 3,
        time: '2025-06-27 08:08:30',
        content: '快件到达【杭州高效物流TM1柠】、地址：商安县尔坊坊升东安长庄淄全活点场服务有限公司】'
      }
    ],
    deliveryTimeline: [
      {
        id: 1,
        time: '2025-06-30 08:18:01',
        content: '在等网"运单异常解决中", 可查看该收入信息'
      },
      {
        id: 2,
        time: '2025-06-30 08:18:50',
        content: '邻警门留言：快件已投放【巢门】，如有疑问请联络快递员【马总总，电话：13261514259】，您的快递送到对我们这关重要，如果您对我们的服务有任何的建议或意见，请联络联系我们，我们一定回心错听，全力改进，不辜负您的信任与支持。'
      },
      {
        id: 3,
        time: '2025-06-30 07:56:43',
        content: '快件到达【北京朝阳回龙慢电商专庄站；地址：朝阳区农光胡里1层花农光胡里里一号177号院同】'
      }
    ],
    orderTickets: [
      {
        operation: '去工单中心编辑',
        ticketId: row.id,
        currentStatusDuration: '53天17小时53分钟',
        priority: '普通',
        type: '物流责任跟进',
        amount: '处理中',
        status: '平台CE',
        workOrderType: '平台CE',
        washCode: '',
        workOrderStatus: '处理中',
        creator: '平台CE',
        currentHandler: '平台CE',
        createTime: row.createTime,
        lastUpdateTime: row.updateTime
      }
    ]
  }
  
  // 显示弹窗
  orderDetailVisible.value = true
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  getWorkOrderList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.currentPage = val
  getWorkOrderList()
}

// 获取时间标签类型
const getTimeTagType = (time) => {
  if (time.includes('天')) {
    const days = parseInt(time.match(/(\d+)天/)?.[1] || 0)
    if (days > 30) return 'danger'
    if (days > 7) return 'warning'
  }
  return 'success'
}

// 获取工单类型名称
const getWorkOrderTypeName = (type) => {
  const typeMap = {
    '1': '洗涤售后',
    '2': '重大风险',
    '3': '大牌补价'
  }
  return typeMap[type] || type
}

// 获取工单状态名称
const getWorkOrderStatusName = (status) => {
  const statusMap = {
    '1': '待处理',
    '2': '处理中',
    '3': '已完结'
  }
  return statusMap[status] || status
}

// 获取工单状态标签类型
const getWorkOrderStatusTagType = (status) => {
  switch (status) {
    case '1': return 'info'      // 待处理
    case '2': return 'warning'   // 处理中
    case '3': return 'success'   // 已完结
    default: return ''
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case '处理中': return 'warning'
    case '已完成': return 'success'
    case '待处理': return 'info'
    default: return ''
  }
}

// 处理工单编辑提交
const handleWorkOrderSubmit = (data) => {
  console.log('工单编辑提交:', data)
  // 这里可以调用API保存工单编辑数据
  workOrderEditVisible.value = false
  // 刷新列表数据
  getWorkOrderList()
}

onMounted(() => {
  getWorkOrderList()
})
</script>

<style lang="scss" scoped>
.work-order {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 91vh;
}

.search-card {
  margin-bottom: 20px;
  
  .search-form {
    .el-form {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .el-form-item {
        margin-bottom: 16px;
      }
    }
  }
  
  .search-buttons {
    margin-top: 16px;
    display: flex;
    gap: 12px;
  }
  
  .filter-tabs {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    gap: 12px;
  }
}

.table-card {
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.el-table {
  font-size: 14px;
  
  :deep(.el-table__header-wrapper) {
    th {
      background-color: #fafafa;
      color: #333;
      font-weight: 600;
    }
  }
  
  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.el-tag {
  font-size: 12px;
}

.el-button {
  &.is-plain {
    border-color: #dcdfe6;
    color: #606266;
    
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
  }
}


</style>