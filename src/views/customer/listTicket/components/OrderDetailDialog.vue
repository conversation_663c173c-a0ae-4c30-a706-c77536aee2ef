<template>
  <el-dialog
    v-model="visible"
    title="订单信息"
    width="80%"
    top="5vh"
    destroy-on-close
    @update:model-value="handleClose"
  >
    <div class="order-detail">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <div class="order-basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">订单编号:</span>
              <span class="value">{{ orderDetail.traceNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前订单状态:</span>
              <span class="value">{{ orderDetail.orderStatus }}</span>
            </div>
            <div class="info-item">
              <span class="label">订单来源:</span>
              <span class="value">{{ orderDetail.orderSource }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ orderDetail.createTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付时间:</span>
              <span class="value">{{ orderDetail.payTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">预约时间:</span>
              <span class="value">{{ orderDetail.appointmentTime }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">送货时间:</span>
              <span class="value">{{ orderDetail.deliveryTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">出厂时间:</span>
              <span class="value">{{ orderDetail.factoryTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">商家备注:</span>
              <span class="value">{{ orderDetail.merchantRemark }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 用户信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">用户信息</span>
        </template>
        <div class="user-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">用户手机号:</span>
              <span class="value">{{ orderDetail.userPhone }}</span>
              <el-tag type="primary" size="small" v-if="orderDetail.isVip">VIP</el-tag>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">取件地址:</span>
              <span class="value">{{ orderDetail.pickupAddress }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">送件地址:</span>
              <span class="value">{{ orderDetail.deliveryAddress }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="label">用户备注:</span>
              <span class="value">{{ orderDetail.userRemark }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 标签页内容 -->
      <el-card class="tabs-card" shadow="never">
        <el-tabs v-model="activeTabName" type="border-card">
          <!-- 衣物信息 -->
          <el-tab-pane label="衣物信息" name="clothing">
            <div class="clothing-summary">
              下单衣物数量: {{ orderDetail.clothingCount }} 实际数量: {{ orderDetail.actualCount }}
            </div>
            <el-table :data="orderDetail.clothingList" stripe>
              <el-table-column prop="name" label="衣物名称" />
              <el-table-column prop="washCode" label="水洗码">
                <template #default="scope">
                  <el-button 
                    type="primary" 
                    link 
                    size="small"
                    @click="handleViewWashProcess(scope.row)"
                  >
                    {{ scope.row.washCode }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="modelNumber" label="货号" />
              <el-table-column prop="memo" label="备件" />
              <el-table-column prop="defect" label="瑕疵" />
              <el-table-column prop="status" label="状态" />
              <el-table-column prop="clothingImage" label="衣物照片">
                <template #default="scope">
                  <el-button 
                    type="primary" 
                    link 
                    size="small"
                    @click="handleViewImage(scope.row)"
                  >
                    查看
                  </el-button>
                </template>
              </el-table-column>

              <!-- 图片预览弹窗 -->
              <ImagePreviewDialog
                v-model="imageDialogVisible"
                :images="currentImages"
                :initial-index="currentImageIndex"
                title="衣物照片"
              />

              <el-table-column prop="beforeWash" label="洗前洗涤" />
              <el-table-column prop="afterWash" label="修止洗涤" />
            </el-table>
            
            <!-- 洗衣流程弹窗 -->
            <WashProcessDialog
              v-model="washProcessVisible"
              :wash-code="currentWashCode"
            />
          </el-tab-pane>

          <!-- 商品信息 -->
          <el-tab-pane label="商品信息" name="product">
            <el-table :data="orderDetail.productList" stripe>
              <el-table-column prop="productName" label="商品名称" />
              <el-table-column prop="quantity" label="数量" />
              <el-table-column prop="unitPrice" label="单价" />
              <el-table-column prop="totalPrice" label="总价" />
            </el-table>
            <div class="price-summary">
              <div class="price-item">
                <span class="label">商品总价:</span>
                <span class="value">¥{{ orderDetail.productTotal }}</span>
              </div>
              <div class="price-item">
                <span class="label">折扣金额:</span>
                <span class="value">¥{{ orderDetail.discount }}</span>
              </div>
              <div class="price-item">
                <span class="label">订单总额:</span>
                <span class="value">¥{{ orderDetail.orderTotal }}</span>
              </div>
              <div class="price-item">
                <span class="label">优惠券:</span>
                <span class="value">¥{{ orderDetail.coupon }}</span>
              </div>
              <div class="price-item">
                <span class="label">现金支付:</span>
                <span class="value">¥{{ orderDetail.cashPayment }}</span>
              </div>
              <div class="price-item">
                <span class="label">应付现金:</span>
                <span class="value">¥{{ orderDetail.payableCash }}</span>
              </div>
              <div class="price-item">
                <span class="label">(直接下单)实付现金:</span>
                <span class="value highlight">¥{{ orderDetail.actualPayment }}</span>
              </div>
              <div class="price-item">
                <span class="label">受付(净销售价+现金部分):</span>
                <span class="value highlight">¥{{ orderDetail.netAmount }}</span>
              </div>
            </div>
          </el-tab-pane>

          <!-- 物流信息 -->
          <el-tab-pane label="物流信息" name="logistics">
            <div class="logistics-tabs">
              <el-tabs v-model="logisticsTab" type="card">
                <el-tab-pane label="取件物流" name="pickup">
                  <div class="logistics-info">
                    <div class="logistics-number">{{ orderDetail.pickupLogisticsNumber }}</div>
                    <div class="logistics-timeline">
                      <el-timeline>
                        <el-timeline-item
                          v-for="item in orderDetail.pickupTimeline"
                          :key="item.id"
                          :timestamp="item.time"
                          placement="top"
                        >
                          {{ item.content }}
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="送件物流" name="delivery">
                  <div class="logistics-info">
                    <div class="logistics-number">{{ orderDetail.deliveryLogisticsNumber }}</div>
                    <div class="logistics-timeline">
                      <el-timeline>
                        <el-timeline-item
                          v-for="item in orderDetail.deliveryTimeline"
                          :key="item.id"
                          :timestamp="item.time"
                          placement="top"
                        >
                          {{ item.content }}
                        </el-timeline-item>
                      </el-timeline>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>

          <!-- 平台工单 -->
          <el-tab-pane label="平台工单" name="platformOrder">
            <div class="platform-loading" v-loading="platformOrderLoading">
              <!-- 平台工单内容会在这里显示 -->
              <div style="height: 200px; display: flex; align-items: center; justify-content: center;">
                <span v-if="!platformOrderLoading">暂无平台工单数据</span>
              </div>
            </div>
          </el-tab-pane>

          <!-- 订单工单 -->
          <el-tab-pane label="订单工单" name="orderTicket">
            <el-table :data="orderDetail.orderTickets" stripe>
              <el-table-column prop="operation" label="操作" width="80">
                <template #default="scope">
                  <el-button type="primary" size="small">去工单中心编辑</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="ticketId" label="工单ID" width="100" />
              <el-table-column prop="currentStatusDuration" label="当前状态持续时长" width="150">
                <template #default="scope">
                  <el-tag :type="getTimeTagType(scope.row.currentStatusDuration)" size="small">
                    {{ scope.row.currentStatusDuration }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="priority" label="优先级" width="80" />
              <el-table-column prop="type" label="类型" width="80" />
              <el-table-column prop="amount" label="金额" width="80" />
              <el-table-column prop="status" label="状态" width="100" />
              <el-table-column prop="workOrderType" label="工单类型" width="120" />
              <el-table-column prop="washShop" label="水洗店" width="180" />
              <el-table-column prop="workOrderStatus" label="工单状态" width="100" />
              <el-table-column prop="creator" label="创建人" width="100" />
              <el-table-column prop="currentHandler" label="当前处理人" width="120" />
              <el-table-column prop="createTime" label="创建时间" width="160" />
              <el-table-column prop="lastUpdateTime" label="最新更新时间" width="160" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import WashProcessDialog from './WashProcessDialog.vue'
import ImagePreviewDialog from './ImagePreviewDialog.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 控制弹窗显示
const visible = ref(props.modelValue)

// 标签页状态
const activeTabName = ref('clothing')
const logisticsTab = ref('pickup')
const platformOrderLoading = ref(false)

// 订单详情数据
const orderDetail = ref({
  traceNumber: '',
  orderStatus: '',
  orderSource: '',
  createTime: '',
  payTime: '',
  appointmentTime: '',
  deliveryTime: '',
  factoryTime: '',
  merchantRemark: '',
  userPhone: '',
  isVip: false,
  pickupAddress: '',
  deliveryAddress: '',
  userRemark: '',
  clothingCount: 0,
  actualCount: 0,
  clothingList: [],
  productList: [],
  productTotal: 0,
  discount: 0,
  orderTotal: 0,
  coupon: 0,
  cashPayment: 0,
  payableCash: 0,
  actualPayment: 0,
  netAmount: 0,
  pickupLogisticsNumber: '',
  deliveryLogisticsNumber: '',
  pickupTimeline: [],
  deliveryTimeline: [],
  orderTickets: []
})

// 监听父组件传入的显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置标签页状态
    activeTabName.value = 'clothing'
    logisticsTab.value = 'pickup'
  }
})

// 监听父组件传入的订单数据
watch(() => props.orderData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    orderDetail.value = { ...orderDetail.value, ...newData }
  }
}, { deep: true, immediate: true })

// 关闭弹窗
const handleClose = (val) => {
  emit('update:modelValue', val)
}

// 获取时间标签类型
const getTimeTagType = (time) => {
  if (time.includes('天')) {
    const days = parseInt(time.match(/(\d+)天/)?.[1] || 0)
    if (days > 30) return 'danger'
    if (days > 7) return 'warning'
  }
  return 'success'
}

// 图片预览相关
const imageDialogVisible = ref(false)
const currentImageIndex = ref(0)
const currentImages = ref([])

// 洗衣流程相关
const washProcessVisible = ref(false)
const currentWashCode = ref('')

// 模拟的衣物照片数据
const mockClothingImages = {
  '101010620322': [
    {
      url: 'https://picsum.photos/800/1000?random=1',
      label: '正面照片',
      time: '2025-06-23 12:30:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=2',
      label: '背面照片',
      time: '2025-06-23 12:30:10'
    },
    {
      url: 'https://picsum.photos/800/1000?random=3',
      label: '细节照片',
      time: '2025-06-23 12:30:20'
    }
  ],
  '101010620346': [
    {
      url: 'https://picsum.photos/800/1000?random=4',
      label: '正面照片',
      time: '2025-06-23 12:35:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=5',
      label: '背面照片',
      time: '2025-06-23 12:35:10'
    }
  ],
  '101010620360': [
    {
      url: 'https://picsum.photos/800/1000?random=6',
      label: '正面照片',
      time: '2025-06-23 12:40:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=7',
      label: '背面照片',
      time: '2025-06-23 12:40:10'
    },
    {
      url: 'https://picsum.photos/800/1000?random=8',
      label: '细节照片1',
      time: '2025-06-23 12:40:20'
    },
    {
      url: 'https://picsum.photos/800/1000?random=9',
      label: '细节照片2',
      time: '2025-06-23 12:40:30'
    }
  ],
  '101010620391': [
    {
      url: 'https://picsum.photos/800/1000?random=10',
      label: '正面照片',
      time: '2025-06-23 12:45:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=11',
      label: '背面照片',
      time: '2025-06-23 12:45:10'
    }
  ],
  '101010620445': [
    {
      url: 'https://picsum.photos/800/1000?random=12',
      label: '正面照片',
      time: '2025-06-23 12:50:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=13',
      label: '背面照片',
      time: '2025-06-23 12:50:10'
    },
    {
      url: 'https://picsum.photos/800/1000?random=14',
      label: '细节照片',
      time: '2025-06-23 12:50:20'
    }
  ]
}

// 查看衣物照片
const handleViewImage = (row) => {
  const images = mockClothingImages[row.washCode] || []
  if (images.length > 0) {
    currentImages.value = images
    currentImageIndex.value = 0
    imageDialogVisible.value = true
  } else {
    ElMessage.warning('暂无衣物照片')
  }
}

// 查看洗衣流程
const handleViewWashProcess = (row) => {
  currentWashCode.value = row.washCode
  washProcessVisible.value = true
}
</script>

<style lang="scss" scoped>
// 订单详情弹窗样式
.order-detail {
  .info-card {
    margin-bottom: 16px;
    
    .card-title {
      font-weight: 600;
      color: #333;
    }
  }
  
  .order-basic-info,
  .user-info {
    .info-row {
      display: flex;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-item {
        flex: 1;
        display: flex;
        align-items: center;
        min-width: 0;
        
        .label {
          color: #666;
          margin-right: 8px;
          white-space: nowrap;
          min-width: 80px;
        }
        
        .value {
          color: #333;
          font-weight: 500;
          word-break: break-all;
        }
        
        .el-tag {
          margin-left: 8px;
        }
      }
    }
  }
  
  .tabs-card {
    .clothing-summary {
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 16px;
      font-size: 14px;
      color: #606266;
    }
    
    .price-summary {
      margin-top: 16px;
      padding: 16px;
      background-color: #f9f9f9;
      border-radius: 4px;
      
      .price-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #666;
        }
        
        .value {
          font-weight: 500;
          
          &.highlight {
            color: #e6a23c;
            font-weight: 600;
          }
        }
      }
    }
    
    .logistics-tabs {
      .logistics-info {
        .logistics-number {
          font-size: 16px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 16px;
          padding: 12px 16px;
          background-color: #f0f9ff;
          border-radius: 4px;
          border-left: 3px solid #409eff;
        }
        
        .logistics-timeline {
          .el-timeline-item {
            margin-bottom: 16px;
          }
        }
      }
    }
    
    .platform-loading {
      min-height: 200px;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}


</style> 