<template>
  <el-dialog
    v-model="visible"
    title="洗衣流程记录"
    width="800px"
    destroy-on-close
    @update:model-value="handleClose"
  >
    <div class="wash-process">
      <!-- 水洗码信息 -->
      <div class="wash-code-info" v-if="props.washCode">
        <span class="label">水洗码:</span>
        <span class="code">{{ props.washCode }}</span>
      </div>
      
      <!-- 流程记录表格 -->
      <el-table :data="processData" stripe v-if="processData.length > 0">
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="operator" label="操作人" />
      </el-table>
      
      <!-- 无数据提示 -->
      <div class="no-data" v-else>
        <el-empty description="暂无洗衣流程记录" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  washCode: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 控制弹窗显示
const visible = ref(props.modelValue)

// 监听父组件传入的显示状态
watch(() => props.modelValue, (newVal) => {
  console.log('WashProcessDialog modelValue changed:', newVal)
  visible.value = newVal
  if (newVal && props.washCode) {
    // 当弹窗打开且有水洗码时，加载数据
    loadProcessData()
  }
})

// 关闭弹窗
const handleClose = (val) => {
  emit('update:modelValue', val)
}

// 模拟的洗衣流程数据
const mockProcessData = {
  '101010620322': [
    { status: '核查中', time: '2025-06-27 11:18:42', operator: '圣洁伊-杨慧洁' },
    { status: '核查中', time: '2025-06-27 11:18:43', operator: '圣洁伊-杨慧洁' },
    { status: '待洗涤', time: '2025-06-27 11:22:20', operator: '圣洁伊-杨慧洁' },
    { status: '洗涤中', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '已洗涤', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '待质检', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '待包装', time: '2025-06-28 10:04:20', operator: '总检-王美丽' },
    { status: '已包装', time: '2025-06-28 10:05:04', operator: '总检-王美丽' },
    { status: '已上架', time: '2025-06-28 10:05:04', operator: '总检-王美丽' },
    { status: '已出库', time: '2025-06-28 10:05:04', operator: '总检-王美丽' }
  ],
  '101010620346': [
    { status: '核查中', time: '2025-06-27 11:18:42', operator: '圣洁伊-杨慧洁' },
    { status: '待洗涤', time: '2025-06-27 11:22:20', operator: '圣洁伊-杨慧洁' },
    { status: '洗涤中', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '已洗涤', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '待质检', time: '2025-06-28 10:04:14', operator: '总检-王美丽' },
    { status: '已出库', time: '2025-06-28 10:05:04', operator: '总检-王美丽' }
  ]
}

// 获取流程数据
const processData = ref([])

// 加载流程数据的方法
const loadProcessData = () => {
  if (props.washCode) {
    console.log('Loading process data for washCode:', props.washCode)
    processData.value = mockProcessData[props.washCode] || []
  } else {
    processData.value = []
  }
}

// 监听washCode变化
watch(() => props.washCode, (newCode) => {
  console.log('WashCode changed:', newCode)
  if (newCode && visible.value) {
    loadProcessData()
  }
}, { immediate: true })

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '核查中': 'info',
    '待洗涤': 'warning',
    '洗涤中': 'warning',
    '已洗涤': 'success',
    '待质检': 'warning',
    '待包装': 'warning',
    '已包装': 'success',
    '已上架': 'success',
    '已出库': 'success'
  }
  return statusMap[status] || ''
}
</script>

<style lang="scss" scoped>
.wash-process {
  .wash-code-info {
    padding: 12px 16px;
    background-color: #f0f9ff;
    border-radius: 4px;
    margin-bottom: 16px;
    border-left: 3px solid #409eff;
    margin-bottom: 40px;
    
    .label {
      color: #666;
      margin-right: 8px;
    }
    
    .code {
      font-weight: 600;
      color: #409eff;
      font-family: 'Consolas', 'Monaco', monospace;
    }
  }
  
  .el-table {
    margin: -20px;
    width: calc(100% + 40px);
  }
  
  .no-data {
    padding: 40px 0;
    text-align: center;
  }
}
</style> 