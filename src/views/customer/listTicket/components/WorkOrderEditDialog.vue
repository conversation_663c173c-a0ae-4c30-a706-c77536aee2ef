<template>
  <el-dialog
    v-model="visible"
    title="工单编辑"
    width="90%"
    top="5vh"
    destroy-on-close
    @update:model-value="handleClose"
  >
    <div class="work-order-edit">
      <!-- 订单和售后信息 -->
      <el-card class="order-info-card" shadow="never">
        <div class="order-info-section">
          <!-- 基本信息行 -->
          <div class="basic-info-row">
            <div class="info-item">
              <span class="label">订单编号</span>
              <el-button 
                type="primary" 
                link 
                size="small"
                @click="handleViewOrderDetail"
                class="order-number-btn"
              >
                {{ orderAfterSalesData.orderNumber }}
              </el-button>
            </div>
            <div class="info-item">
              <span class="label">下单衣物数量</span>
              <span class="value">{{ orderAfterSalesData.orderClothingCount }}</span>
            </div>
            <div class="info-item">
              <span class="label">实收衣物数量</span>
              <span class="value">{{ orderAfterSalesData.actualClothingCount }}</span>
            </div>
          </div>
          
          <div class="basic-info-row">
            <div class="info-item">
              <span class="label">用户备注</span>
              <span class="value">{{ orderAfterSalesData.userRemark }}</span>
            </div>
            <div class="info-item">
              <span class="label">用户手机号</span>
              <span class="value">{{ orderAfterSalesData.userPhone }}</span>
            </div>
            <div class="info-item">
              <span class="label">商家备注</span>
              <span class="value">{{ orderAfterSalesData.merchantRemark }}</span>
            </div>
          </div>
        </div>
        
        <!-- 售后信息表格 -->
        <div class="after-sales-section">
          <div class="section-title">售后信息</div>
          <el-table :data="orderAfterSalesData.afterSalesItems" border size="small">
            <el-table-column prop="number" label="退款编号" width="80" align="center" />
            <el-table-column prop="type" label="类型" width="120" align="center" />
            <el-table-column prop="status" label="审核状态" align="center" />
            <el-table-column prop="breakdown" label="责任划分" align="center" />
            <el-table-column prop="amount" label="金额" width="80" align="center" />
            <el-table-column prop="createInfo" label="创建" align="center" />
            <el-table-column prop="timeInfo" label="付款/审核时间" width="180" align="center" />
            <el-table-column prop="note" label="备注" align="center" />
          </el-table>
        </div>
      </el-card>

      <!-- 工单基本信息 -->
      <el-card class="info-card" shadow="never">
        <div class="work-order-header">
          <div class="order-tabs">
            <el-tag 
              :type="activeOrderTab === 'current' ? 'primary' : 'info'"
              @click="activeOrderTab = 'current'"
              class="order-tab"
            >
              {{ workOrderData.id }}-{{ workOrderData.workOrderTitle }}
            </el-tag>
          </div>
          
          <div class="work-order-info">
            <div class="info-row">
              <div class="info-item">
                <span class="label">工单标题:</span>
                <span class="value">{{ workOrderData.workOrderTitle }}</span>
                <el-button type="primary" link size="small" @click="showTitleEditDialog">修改</el-button>
              </div>
              <div class="info-item">
                <span class="label">工单标签:</span>
                <span class="value">{{ workOrderData.labelId }}</span>
                <el-button type="primary" link size="small" @click="showTagsEditDialog">修改</el-button>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">工单类型:</span>
                <span class="value">{{ getWorkOrderTypeName(workOrderData.workOrderType) }}</span>
              </div>
              <div class="info-item">
                <span class="label">工单状态:</span>
                <span class="value">{{ getWorkOrderStatusName(workOrderData.workOrderStatus) }}</span>
              </div>
              <div class="info-item">
                <span class="label">优先级:</span>
                <span class="value">{{ workOrderData.priorityLevel }}</span>
              </div>
              <div class="info-item">
                <span class="label">水洗码:</span>
                <span class="value">{{ workOrderData.washCode }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">订单编号:</span>
                <span class="value">{{ workOrderData.orderNo }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ workOrderData.createAt }}</span>
              </div>
              <div class="info-item">
                <span class="label">更新时间:</span>
                <span class="value">{{ workOrderData.updateAt }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作标签页 -->
      <el-card class="tabs-card" shadow="never">
        <el-tabs v-model="activeTabName" type="border-card">
          <!-- 更新工单 -->
          <el-tab-pane label="更新工单" name="update">
            <el-alert
                title="提交后工单状态不变"
                type="info"
                show-icon
                :closable="false"
              />
            <div class="form-section">
              <el-form :model="updateForm" label-width="100px">
                <el-form-item label="备注:">
                  <el-input
                    v-model="updateForm.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
                
                <el-form-item label="上传凭证:">
                  <div class="upload-area">
                    <FileUpload
                      v-model="updateForm.credentialsUrl"
                      :limit="1"
                      list-type="picture-card"
                      :fileSize="10"
                      :fileType="['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx']"
                    />
                  </div>
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmitUpdate">提交</el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 已完结 - 只有非已完结状态才显示 -->
          <el-tab-pane 
            v-if="workOrderData.workOrderStatus !== '3'"
            label="已完结" 
            name="completed"
          >
            <el-alert
                title="提交后工单完结"
                type="info"
                show-icon
                :closable="false"
              />
            <div class="form-section">
              <el-form :model="completedForm" label-width="100px">
                <el-form-item label="备注:">
                  <el-input
                    v-model="completedForm.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
                
                <el-form-item label="上传凭证:">
                  <div class="upload-area">
                    <FileUpload
                      v-model="completedForm.credentialsUrl"
                      :limit="1"
                      :fileSize="10"
                      list-type="picture-card"
                      :fileType="['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx']"
                    />
                  </div>
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmitCompleted">提交</el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 退款 - 只有非已完结状态才显示 -->
          <el-tab-pane 
            v-if="workOrderData.workOrderStatus !== '3'"
            label="退款" 
            name="refund"
          >
            <div class="refund-section">
              <el-alert
                title="提交后会将补救款到短信给用户，超48小时未补款的，补款条将取消，勾选衣物确认为不洗"
                type="info"
                show-icon
                :closable="false"
              />
              
              <el-form :model="refundForm" label-width="100px" style="margin-top: 20px;">
                <el-form-item label="退款链接:">
                  <el-checkbox v-model="refundForm.refundLink">
                    给用户发短信（自备是通过了不给用户发短信）
                  </el-checkbox>
                </el-form-item>
                
                <el-form-item label="补款金额:">
                  <el-input
                    v-model="refundForm.supplementAmount"
                    placeholder="请输入补款金额"
                    style="width: 200px;"
                  />
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmitRefund">提交</el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 发短信 - 只有非已完结状态才显示 -->
          <el-tab-pane 
            v-if="workOrderData.workOrderStatus !== '3'"
            label="发短信" 
            name="sms"
          >
            <div class="sms-section">
              <el-form :model="smsForm" label-width="100px">
                <el-form-item label="用户手机:">
                  <el-input
                    v-model="smsForm.userPhone"
                    placeholder="用户手机号"
                    style="width: 200px;"
                  />
                </el-form-item>
                
                <el-form-item label="通知类型:">
                  <el-radio-group v-model="smsForm.notificationType">
                    <el-radio value="image">图片采集</el-radio>
                    <el-radio value="photo">发图片</el-radio>
                    <el-radio value="value">异常侦值推送</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmitSMS">提交</el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 补差价 - 只有非已完结状态才显示 -->
          <el-tab-pane 
            v-if="workOrderData.workOrderStatus !== '3'"
            label="补差价" 
            name="priceDiff"
          >
            <div class="price-diff-section">
              <el-alert
                title="提交后会将补救款到短信给用户，超48小时未补款的，补款条将取消，勾选衣物确认为不洗"
                type="info"
                show-icon
                :closable="false"
              />
              
              <el-form :model="priceDiffForm" label-width="100px" style="margin-top: 20px;">
                <el-form-item label="补差价名称:">
                  <el-radio-group v-model="priceDiffForm.priceDiffType">
                    <el-radio value="add">数量加洗</el-radio>
                    <el-radio value="special">特殊品类补差价</el-radio>
                    <el-radio value="modify">修改品类补差价</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="补款金额:">
                  <el-input
                    v-model="priceDiffForm.supplementAmount"
                    placeholder="请为衣物"
                    style="width: 300px;"
                  />
                </el-form-item>
                
                <el-form-item label="上传凭证:">
                  <div class="upload-area">
                    <FileUpload
                      v-model="priceDiffForm.credentialsUrl"
                      :limit="1"
                       list-type="picture-card"
                      :fileSize="10"
                      :fileType="['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx']"
                    />
                  </div>
                </el-form-item>
              </el-form>
              
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmitPriceDiff">提交</el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 衣物信息 -->
      <el-card class="clothing-card" shadow="never">
        <template #header>
          <span class="card-title">衣物信息</span>
        </template>
        
        <el-table :data="clothingData" stripe>
          <el-table-column prop="name" label="衣物名称" />
          <el-table-column prop="washCode" label="水洗码" width="140">
            <template #default="scope">
              <el-button 
                type="primary" 
                link 
                size="small"
                @click="handleViewWashProcess(scope.row)"
              >
                {{ scope.row.washCode }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="washMethod" label="洗涤方式" >
            <template #default="scope">
              <el-select 
                v-model="scope.row.washMethod" 
                placeholder="选择洗涤方式"
                size="small"
                @change="handleWashMethodChange(scope.row, scope.$index)"
              >
                <el-option label="精洗" value="精洗" />
                <el-option label="补价" value="补价" />
                <el-option label="不洗" value="不洗" />
                <el-option label="常规洗涤" value="常规洗涤" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="accessory" label="附件" />
          <el-table-column prop="defect" label="瑕疵" />
          <el-table-column prop="serialNumber" label="检货号" />
          <el-table-column prop="image" label="衣物照片" >
            <template #default="scope">
              <div class="clothing-image">
                <el-image
                  style="width: 40px; height: 40px"
                  :src="scope.row.image"
                  fit="cover"
                  @click="handleViewClothingImage(scope.row)"
                  class="clickable-image"
                />
                <el-button 
                  type="primary" 
                  link 
                  size="small"
                  @click="handleViewClothingImage(scope.row)"
                >
                  查看
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" />
          <el-table-column prop="beforeWash" label="是否选洗" />
          <el-table-column prop="afterWash" label="修止洗涤" />
          <el-table-column prop="factoryTime" label="在厂时间" />
        </el-table>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="operation-records-card" shadow="never">
        <template #header>
          <span class="card-title">操作记录</span>
        </template>
        
        <div class="operation-records">
          <div 
            v-for="(record, index) in operationRecords" 
            :key="index" 
            class="operation-item"
          >
            <div class="operation-header">
              <span class="operation-type">{{ record.type }}</span>
              <span class="operation-time">{{ record.time }}</span>
            </div>
            <div class="operation-content">
              <div class="operation-user">
                工厂：{{ record.factory }} 
                <span v-if="record.phone">手机号码：{{ record.phone }}</span>
              </div>
              <div class="operation-description">
                推送原因：{{ record.reason }} {{ record.description }}
              </div>
              <div v-if="record.message" class="operation-message">
                短信类型：{{ record.message }}
              </div>
            </div>
            <div v-if="record.images && record.images.length > 0" class="operation-images">
              <span class="images-label">风险描述：</span>
              <div class="images-list">
                <span v-for="(image, imgIndex) in record.images" :key="imgIndex">
                  {{ image.code }} {{ image.description }}{{ imgIndex < record.images.length - 1 ? '，' : '。' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </el-dialog>

  <!-- 衣物照片预览弹窗 -->
  <ImagePreviewDialog
    v-model="clothingImageVisible"
    :images="currentClothingImages"
    :initial-index="currentClothingImageIndex"
    title="衣物照片"
  />

  <!-- 订单详情弹窗组件 -->
  <OrderDetailDialog 
    v-model="orderDetailVisible" 
    :order-data="orderDetailData" 
  />

  <!-- 洗衣流程弹窗 -->
  <WashProcessDialog
    v-model="washProcessVisible"
    :wash-code="currentWashCode"
  />

  <!-- 修改工单标题弹窗 -->
  <el-dialog
    v-model="titleEditVisible"
    title="修改工单标题"
    width="500px"
    destroy-on-close
  >
    <el-form :model="titleEditForm" label-width="80px">
      <el-form-item label="标题:">
        <el-input
          v-model="titleEditForm.title"
          placeholder="请输入工单标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="titleEditVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTitleEdit">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 修改工单标签弹窗 -->
  <el-dialog
    v-model="tagsEditVisible"
    title="修改工单标签"
    width="500px"
    destroy-on-close
  >
    <el-form :model="tagsEditForm" label-width="80px">
      <el-form-item label="标签:">
        <el-checkbox-group v-model="tagsEditForm.selectedTags">
          <el-checkbox value="urgent">紧急</el-checkbox>
          <el-checkbox value="modify">修改</el-checkbox>
          <el-checkbox value="refund">退款</el-checkbox>
          <el-checkbox value="compensation">赔偿</el-checkbox>
          <el-checkbox value="quality">质量问题</el-checkbox>
          <el-checkbox value="delivery">配送问题</el-checkbox>
          <el-checkbox value="other">其他</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="tagsEditVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTagsEdit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getInfo, updateInfo } from '@/api/system/wokerOrder'
import ImagePreviewDialog from './ImagePreviewDialog.vue'
import OrderDetailDialog from './OrderDetailDialog.vue'
import WashProcessDialog from './WashProcessDialog.vue'
import FileUpload from '@/components/FileUpload/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  workOrderId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'submit'])

// 控制弹窗显示
const visible = ref(props.modelValue)

// 编辑弹窗控制
const titleEditVisible = ref(false)
const tagsEditVisible = ref(false)

// 衣物照片预览控制
const clothingImageVisible = ref(false)
const currentClothingImages = ref([])
const currentClothingImageIndex = ref(0)

// 订单详情弹窗控制
const orderDetailVisible = ref(false)
const orderDetailData = ref({})

// 洗衣流程弹窗控制
const washProcessVisible = ref(false)
const currentWashCode = ref('')

// 标签页状态
const activeTabName = ref('update')
const activeOrderTab = ref('current')

// 加载状态
const loading = ref(false)

// 监听父组件传入的显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.workOrderId) {
    // 重置状态
    activeTabName.value = 'update'
    activeOrderTab.value = 'current'
    loadWorkOrderData()
  }
})

// 关闭弹窗
const handleClose = (val) => {
  emit('update:modelValue', val)
}

// 工单数据
const workOrderData = ref({
  id: '',
  workOrderTitle: '',
  workOrderType: '',
  workOrderStatus: '',
  labelId: '',
  washCode: '',
  priorityLevel: '',
  orderNo: '',
  remark: '',
  credentialsUrl: '',
  clothingId: '',
  createAt: '',
  updateAt: ''
})

// 订单和售后信息数据
const orderAfterSalesData = ref({
  orderNumber: 'M024643696433066',
  orderClothingCount: 3,
  actualClothingCount: 3,
  userRemark: '',
  userPhone: '***********',
  merchantRemark: '',
  afterSalesItems: [
    {
      number: '252',
      type: '订单退款',
      status: '',
      breakdown: '平台承担金额:\n工厂承担金额:',
      amount: '18.3',
      createInfo: '创建退款数据错误',
      timeInfo: '申请时间: 2023-09-07 09:10:39\n审核时间: 2023-09-07 09:11:11',
      note: ''
    }
  ]
})

// 表单数据
const updateForm = ref({
  remark: '',
  credentialsUrl: ''
})

const completedForm = ref({
  remark: '',
  credentialsUrl: ''
})

// 编辑表单数据
const titleEditForm = ref({
  title: ''
})

const tagsEditForm = ref({
  selectedTags: []
})

const refundForm = ref({
  refundLink: false,
  supplementAmount: ''
})

const smsForm = ref({
  userPhone: '15847460842',
  notificationType: 'image'
})

const priceDiffForm = ref({
  priceDiffType: 'add',
  supplementAmount: '',
  credentialsUrl: ''
})

// 衣物数据
const clothingData = ref([
  {
    name: '休闲鞋（不区分绒面非绒面）',
    washCode: '10101069135578',
    washMethod: '常规洗涤',
    accessory: '',
    defect: '否',
    serialNumber: '',
    image: 'https://picsum.photos/100/100?random=1',
    status: '待洗',
    beforeWash: '',
    afterWash: '',
    factoryTime: '1天3小时27分钟'
  },
  {
    name: '绒面靴',
    washCode: '10101069135592',
    washMethod: '常规洗涤',
    accessory: '',
    defect: '否',
    serialNumber: '',
    image: 'https://picsum.photos/100/100?random=2',
    status: '待洗',
    beforeWash: '',
    afterWash: '',
    factoryTime: '1天3小时27分钟'
  },
  {
    name: '绒面靴',
    washCode: '10101069136593',
    washMethod: '常规洗涤',
    accessory: '',
    defect: '否',
    serialNumber: '',
    image: 'https://picsum.photos/100/100?random=3',
    status: '待洗',
    beforeWash: '',
    afterWash: '',
    factoryTime: '1天3小时27分钟'
  }
])

// 模拟的衣物照片数据
const mockClothingImages = {
  '10101069135578': [
    {
      url: 'https://picsum.photos/800/1000?random=1',
      label: '收衣照片',
      time: '2025-08-21 10:30:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=2',
      label: '洗前照片',
      time: '2025-08-21 14:20:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=3',
      label: '洗后照片',
      time: '2025-08-22 16:45:00'
    }
  ],
  '10101069135592': [
    {
      url: 'https://picsum.photos/800/1000?random=4',
      label: '收衣照片',
      time: '2025-08-21 10:35:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=5',
      label: '洗前照片',
      time: '2025-08-21 14:25:00'
    }
  ],
  '10101069136593': [
    {
      url: 'https://picsum.photos/800/1000?random=6',
      label: '收衣照片',
      time: '2025-08-21 10:40:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=7',
      label: '洗前照片',
      time: '2025-08-21 14:30:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=8',
      label: '洗后照片',
      time: '2025-08-22 16:50:00'
    },
    {
      url: 'https://picsum.photos/800/1000?random=9',
      label: '质检照片',
      time: '2025-08-22 18:00:00'
    }
  ]
}

// 操作记录数据
const operationRecords = ref([
  {
    type: '操作记录',
    time: '2025-08-21 11:38:50',
    factory: '【发短信】',
    phone: '15847460842',
    reason: '【未接通】',
    description: '重大损坏或呈阳性，短信类型:异常短信推送',
    message: '异常短信推送'
  },
  {
    type: '客房序-字符码',
    time: '2025-08-20 13:36:51',
    factory: '【未接通】',
    phone: '15847460842',
    reason: '【未接通】',
    description: '重大损坏或呈阳性内容短信类型：异常短信推送',
    message: '异常短信推送'
  },
  {
    type: '平台CE',
    time: '2025-08-20 10:31:32',
    factory: '【创建重大风险工单】',
    reason: '',
    description: '',
    images: [
      { code: '10101069113578', description: '腿面/副毛皮、洗后可能会染色、发白发硬、拍皮' },
      { code: '10101069113592', description: '腿面/副毛皮、洗后可能会染色、发白发硬、拍皮' },
      { code: '10101069113653', description: '腿面/副毛皮、洗后可能会染色、发白发硬、拍皮' }
    ]
  }
])

// 加载工单数据
const loadWorkOrderData = async () => {
  if (!props.workOrderId) return
  
  loading.value = true
  
  try {
    const response = await getInfo(props.workOrderId)
    
    if (response.code === 200) {
      const data = response.data
      
      // 填充工单基本信息
      workOrderData.value = {
        id: data.id,
        workOrderTitle: data.workOrderTitle || '',
        workOrderType: data.workOrderType || '',
        workOrderStatus: data.workOrderStatus || '',
        labelId: data.labelId || '',
        washCode: data.washCode || '',
        priorityLevel: data.priorityLevel || '',
        orderNo: data.orderNo || '',
        remark: data.remark || '',
        credentialsUrl: data.credentialsUrl || '',
        clothingId: data.clothingId || '',
        createAt: data.createAt || '',
        updateAt: data.updateAt || ''
      }
      
      // 填充表单数据
      updateForm.value.remark = data.remark || ''
      updateForm.value.credentialsUrl = data.credentialsUrl || ''
      
      completedForm.value.remark = data.remark || ''
      completedForm.value.credentialsUrl = data.credentialsUrl || ''
      
      // 只更新订单编号，其他信息保持模拟数据
      if (data.orderNo) {
        orderAfterSalesData.value.orderNumber = data.orderNo
      }
      
      // 如果工单已完结，确保激活的标签页是update
      if (data.workOrderStatus === '3' && activeTabName.value !== 'update') {
        activeTabName.value = 'update'
      }
      
    } else {
      ElMessage.error(response.msg || '获取工单详情失败')
    }
  } catch (error) {
    console.error('获取工单详情失败:', error)
    ElMessage.error('获取工单详情失败')
  } finally {
    loading.value = false
  }
}

// 获取工单类型名称
const getWorkOrderTypeName = (type) => {
  const typeMap = {
    '1': '洗涤售后',
    '2': '重大风险',
    '3': '大牌补价'
  }
  return typeMap[type] || type
}

// 获取工单状态名称
const getWorkOrderStatusName = (status) => {
  const statusMap = {
    '1': '待处理',
    '2': '处理中',
    '3': '已完结'
  }
  return statusMap[status] || status
}

// 洗涤方式变更处理
const handleWashMethodChange = (row, index) => {
  console.log('Wash method changed:', row.washCode, row.washMethod)
  ElMessage.success(`${row.name} 洗涤方式已更改为：${row.washMethod}`)
}

// 查看衣物照片
const handleViewClothingImage = (row) => {
  const images = mockClothingImages[row.washCode] || []
  if (images.length > 0) {
    currentClothingImages.value = images
    currentClothingImageIndex.value = 0
    clothingImageVisible.value = true
  } else {
    ElMessage.warning('暂无衣物照片')
  }
}

// 查看洗衣流程
const handleViewWashProcess = (row) => {
  currentWashCode.value = row.washCode
  washProcessVisible.value = true
}

// 查看订单详情
const handleViewOrderDetail = () => {
  // 构造订单详情数据
  orderDetailData.value = {
    traceNumber: orderAfterSalesData.value.orderNumber,
    orderStatus: '已完成',
    orderSource: '小程序',
    createTime: workOrderData.value.createAt,
    payTime: '',
    appointmentTime: '',
    deliveryTime: '',
    factoryTime: '',
    merchantRemark: '',
    userPhone: orderAfterSalesData.value.userPhone,
    isVip: false,
    pickupAddress: '',
    deliveryAddress: '',
    userRemark: orderAfterSalesData.value.userRemark,
    clothingCount: orderAfterSalesData.value.orderClothingCount,
    actualCount: orderAfterSalesData.value.actualClothingCount,
    clothingList: [],
    productList: [],
    productTotal: 0,
    discount: 0,
    orderTotal: 0,
    coupon: 0,
    cashPayment: 0,
    payableCash: 0,
    actualPayment: 0,
    netAmount: 0,
    pickupLogisticsNumber: '',
    deliveryLogisticsNumber: '',
    pickupTimeline: [],
    deliveryTimeline: [],
    orderTickets: []
  }
  
  orderDetailVisible.value = true
}

// 提交更新工单
const handleSubmitUpdate = async () => {
  if (!workOrderData.value.id) {
    ElMessage.error('工单ID不存在')
    return
  }
  
  loading.value = true
  
  try {
    const updateData = {
      id: workOrderData.value.id,
      workOrderStatus: '2', // 更新工单状态为2
      remark: updateForm.value.remark,
      credentialsUrl: updateForm.value.credentialsUrl
    }
    
    const response = await updateInfo(updateData)
    
    if (response.code === 200) {
      ElMessage.success('工单更新成功')
      emit('submit', { type: 'update', data: updateData })
      handleClose(false)
    } else {
      ElMessage.error(response.msg || '工单更新失败')
    }
  } catch (error) {
    console.error('工单更新失败:', error)
    ElMessage.error('工单更新失败')
  } finally {
    loading.value = false
  }
}

// 提交完结工单
const handleSubmitCompleted = async () => {
  if (!workOrderData.value.id) {
    ElMessage.error('工单ID不存在')
    return
  }
  
  loading.value = true
  
  try {
    const updateData = {
      id: workOrderData.value.id,
      workOrderStatus: '3', // 已完结状态为3
      remark: completedForm.value.remark,
      credentialsUrl: completedForm.value.credentialsUrl
    }
    
    const response = await updateInfo(updateData)
    
    if (response.code === 200) {
      ElMessage.success('工单已完结')
      emit('submit', { type: 'completed', data: updateData })
      handleClose(false)
    } else {
      ElMessage.error(response.msg || '工单完结失败')
    }
  } catch (error) {
    console.error('工单完结失败:', error)
    ElMessage.error('工单完结失败')
  } finally {
    loading.value = false
  }
}

const handleSubmitRefund = () => {
  console.log('Submitting refund:', refundForm.value)
  ElMessage.success('退款申请已提交')
  emit('submit', { type: 'refund', data: refundForm.value })
}

const handleSubmitSMS = () => {
  console.log('Submitting SMS:', smsForm.value)
  ElMessage.success('短信已发送')
  emit('submit', { type: 'sms', data: smsForm.value })
}

const handleSubmitPriceDiff = () => {
  console.log('Submitting price diff:', priceDiffForm.value)
  ElMessage.success('补差价已提交')
  emit('submit', { type: 'priceDiff', data: priceDiffForm.value })
}

// 编辑弹窗方法
const showTitleEditDialog = () => {
  titleEditForm.value.title = workOrderData.value.workOrderTitle
  titleEditVisible.value = true
}

const showTagsEditDialog = () => {
  // 将当前标签转换为数组形式
  const labelIds = workOrderData.value.labelId ? workOrderData.value.labelId.split(',') : []
  tagsEditForm.value.selectedTags = labelIds
  tagsEditVisible.value = true
}

const handleTitleEdit = async () => {
  if (!titleEditForm.value.title.trim()) {
    ElMessage.warning('请输入工单标题')
    return
  }
  
  try {
    const updateData = {
      id: workOrderData.value.id,
      workOrderTitle: titleEditForm.value.title
    }
    
    const response = await updateInfo(updateData)
    
    if (response.code === 200) {
      workOrderData.value.workOrderTitle = titleEditForm.value.title
      titleEditVisible.value = false
      ElMessage.success('工单标题修改成功')
      emit('submit', { type: 'titleEdit', data: updateData })
    } else {
      ElMessage.error(response.msg || '工单标题修改失败')
    }
  } catch (error) {
    console.error('工单标题修改失败:', error)
    ElMessage.error('工单标题修改失败')
  }
}

const handleTagsEdit = async () => {
  try {
    const updateData = {
      id: workOrderData.value.id,
      labelId: tagsEditForm.value.selectedTags.join(',')
    }
    
    const response = await updateInfo(updateData)
    
    if (response.code === 200) {
      workOrderData.value.labelId = updateData.labelId
      tagsEditVisible.value = false
      ElMessage.success('工单标签修改成功')
      emit('submit', { type: 'tagsEdit', data: updateData })
    } else {
      ElMessage.error(response.msg || '工单标签修改失败')
    }
  } catch (error) {
    console.error('工单标签修改失败:', error)
    ElMessage.error('工单标签修改失败')
  }
}
</script>

<style lang="scss" scoped>
.work-order-edit {
  .order-info-card {
    margin-bottom: 16px;
    
    .order-info-section {
      .basic-info-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 24px;
        
        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .label {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
          }
          
          .value {
            color: #333;
            font-size: 14px;
          }
        }
        
        .action-item {
          margin-left: auto;
        }
        
        .order-number-btn {
          font-size: 14px;
          padding: 0;
          height: auto;
          font-weight: 500;
        }
      }
    }
    
    .after-sales-section {
      margin-top: 16px;
      
      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }
    }
  }
  
  .info-card {
    margin-bottom: 16px;
    
    .work-order-header {
      .order-tabs {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
        
        .order-tab {
          cursor: pointer;
          padding: 8px 16px;
          transition: all 0.3s;
          
          &:hover {
            opacity: 0.8;
          }
        }
      }
      
      .work-order-info {
        .info-row {
          display: flex;
          gap: 24px;
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .label {
              color: #666;
              font-size: 14px;
              white-space: nowrap;
            }
            
            .value {
              color: #333;
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  
  .tabs-card {
    margin-bottom: 16px;
    
    .form-section,
    .completed-info,
    .refund-section,
    .sms-section,
    .price-diff-section {
      padding: 16px 0;
      
      .upload-area {
        .upload-demo {
          width: 100%;
          max-width: 400px;
        }
      }
      
      .form-actions {
        margin-top: 24px;
        text-align: center;
        
        .el-button {
          padding: 12px 40px;
        }
      }
    }
  }
  
  .clothing-card,
  .operation-records-card {
    .card-title {
      font-weight: 600;
      color: #333;
    }
    
    .clothing-image {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      
      .clickable-image {
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
      
      .el-button {
        font-size: 12px;
      }
    }
  }
  
  .operation-records-card {
    margin-top: 16px;
    
    .operation-records {
      .operation-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .operation-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .operation-type {
            font-weight: 600;
            color: #333;
            font-size: 14px;
          }
          
          .operation-time {
            color: #666;
            font-size: 12px;
          }
        }
        
        .operation-content {
          margin-bottom: 8px;
          
          .operation-user,
          .operation-description,
          .operation-message {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 4px;
          }
        }
        
        .operation-images {
          .images-label {
            color: #666;
            font-size: 13px;
          }
          
          .images-list {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
            margin-top: 4px;
          }
        }
      }
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

:deep(.el-tabs--border-card) {
  .el-tabs__content {
    padding: 20px;
  }
}

:deep(.el-upload-dragger) {
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
  
  &:hover {
    border-color: #409eff;
  }
}

:deep(.el-alert) {
  margin-bottom: 16px;
}
</style> 