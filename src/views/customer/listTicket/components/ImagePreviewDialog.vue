<template>
  <el-dialog
    v-model="visible"
    title="图片预览"
    width="800px"
    append-to-body
    destroy-on-close
    center
    @update:model-value="handleClose"
  >
    <div class="image-preview" v-if="images.length > 0">
      <el-carousel 
        height="500px" 
        :initial-index="initialIndex"
        indicator-position="outside"
        arrow="always"
      >
        <el-carousel-item v-for="(image, index) in images" :key="index">
          <div class="image-wrapper">
            <el-image
              :src="image.url"
              fit="contain"
              :preview-src-list="images.map(img => img.url)"
              hide-on-click-modal
              :z-index="9999"
              :preview-teleported="true"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <el-icon><Loading /></el-icon>
                  <span>加载中...</span>
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
            <div class="image-info" v-if="image.label || image.time">
              <span class="label">{{ image.label || '图片' }}</span>
              <span class="time" v-if="image.time">{{ image.time }}</span>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div v-else class="no-images">
      <el-icon><Picture /></el-icon>
      <span>暂无图片</span>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Loading, Picture } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  images: {
    type: Array,
    default: () => []
  },
  initialIndex: {
    type: Number,
    default: 0
  },
  title: {
    type: String,
    default: '图片预览'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 控制弹窗显示
const visible = ref(props.modelValue)

// 监听父组件传入的显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 关闭弹窗
const handleClose = (val) => {
  emit('update:modelValue', val)
}
</script>

<style lang="scss" scoped>
.image-preview {
  .image-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .el-image {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
      
      .image-placeholder,
      .image-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        font-size: 14px;
        
        .el-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
      }
    }
    
    .image-info {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      background-color: #f5f7fa;
      border-top: 1px solid #e4e7ed;
      
      .label {
        font-weight: 500;
        color: #333;
      }
      
      .time {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}

.no-images {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

:deep(.el-carousel__indicators) {
  bottom: -25px;
}
</style> 