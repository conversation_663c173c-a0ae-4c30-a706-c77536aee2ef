<template>
  <div class="page-container">
    <el-card shadow="never">
      <el-form :model="sizeForm" :rules="formRules" ref="formRef" label-width="auto" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="工单标题" prop="workOrderTitle">
              <el-input v-model="sizeForm.workOrderTitle" placeholder="请输入工单标题" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="工单类型" prop="workOrderType">
              <el-select v-model="sizeForm.workOrderType" placeholder="请选择工单类型">
                <el-option label="洗涤售后" value="1" />
                <el-option label="重大风险" value="2" />
                <el-option label="大牌补价" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="水洗码" prop="washCode">
              <div style="display: flex; align-items: center;">
                <el-input v-model="sizeForm.washCode" style="width: 100%;" placeholder="请输入水洗码"/>
                <el-button type="primary" style="margin-left: 10px;" @click="getWashInfo" :loading="washCodeLoading">获取信息</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优先级">
              <el-select v-model="sizeForm.priorityLevel" placeholder="请选择">
                <el-option label="普通" value="普通" />
                <el-option label="重要" value="重要" />
                <el-option label="紧急" value="紧急" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="工单标签" prop="labelId">
              <el-checkbox-group v-model="sizeForm.labelId">
                <el-checkbox v-for="item in currentLabels" :key="item.value" :label="item.label" :value="item.value" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="用户姓名">
              <el-input v-model="sizeForm.userName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号">
              <el-input v-model="sizeForm.userPhone" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="用户地址">
              <el-input v-model="sizeForm.userAddress" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单编号">
              <el-input v-model="sizeForm.orderNo" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="备注信息">
              <el-input v-model="sizeForm.remark" :autosize="{ minRows: 6, maxRows: 6 }" type="textarea"
                placeholder="备注信息" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传凭证">
              <FileUpload 
                v-model="credentialsUrl"
                :limit="1"
                list-type="picture-card"
                :fileSize="10"
                :fileType="['jpg', 'png', 'jpeg', 'pdf', 'doc', 'docx']"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="勾选衣物" prop="selectedClothes">
              <div style="margin: 10px 0;">
                <el-alert v-if="!sizeForm.washCode" title="请先输入水洗码获取衣物信息" type="warning" show-icon :closable="false" />
                <div v-else>
                  <div style="margin-bottom: 10px;">
                    <el-button type="primary" size="small" @click="selectAllClothes">全选</el-button>
                    <el-button size="small" @click="clearAllClothes">清空</el-button>
                    <span style="margin-left: 10px; color: #909399;">已选择 {{ sizeForm.selectedClothes.length }} 件衣物</span>
                  </div>
                </div>
              </div>
              <el-table :data="clothesData" style="width: 100%" @selection-change="handleClothesSelection">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="washCode" label="水洗码" width="120" />
                <el-table-column prop="name" label="衣物名称" width="150" />
                <el-table-column prop="washMethod" label="洗涤方式" width="120" />
                <el-table-column prop="accessories" label="凭证" width="100" />
                <el-table-column prop="defects" label="瑕疵" width="120" />
                <el-table-column prop="shelfNumber" label="格架号" width="100" />
                <el-table-column prop="image" label="衣物图片" width="100">
                  <template #default="{ row }">
                    <el-image v-if="row.image" :src="row.image" style="width: 40px; height: 40px;" fit="cover" />
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="isRewash" label="是否返洗" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.isRewash ? 'warning' : 'success'">{{ row.isRewash ? '是' : '否' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="stopWash" label="终止洗涤" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.stopWash ? 'danger' : 'info'">{{ row.stopWash ? '是' : '否' }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="inFactoryTime" label="在厂时间" width="160" />
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" :loading="submitLoading">创建工单</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

    </el-card>
    <el-dialog v-model="dialogVisible">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { addInfo } from '@/api/system/wokerOrder'
import FileUpload from '@/components/FileUpload/index.vue'

const fileList = ref([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const formRef = ref()
const washCodeLoading = ref(false)
const submitLoading = ref(false)

// 凭证文件上传
const credentialsUrl = ref('')

const handleRemove = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url
  dialogVisible.value = true
}

const sizeForm = reactive({
  workOrderTitle: '', // 工单标题
  workOrderType: '', // 工单类型
  washCode: '', // 水洗码
  priorityLevel: '', // 优先级
  labelId: [], // 工单标签ID
  remark: '', // 备注信息
  userName: '',
  userPhone: '',
  userAddress: '',
  selectedClothes: [], // 选中的衣物
  orderNo: '', // 订单编号
  workOrderStatus: '1' // 工单状态
})

// 表单校验规则
const formRules = {
  workOrderTitle: [
    { required: true, message: '请输入工单标题', trigger: 'blur' },
    { min: 2, max: 50, message: '工单标题长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  workOrderType: [
    { required: true, message: '请选择工单类型', trigger: 'change' }
  ],
  washCode: [
    { required: true, message: '请输入水洗码', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]{1,20}$/, message: '水洗码格式不正确', trigger: 'blur' }
  ],
  labelId: [
    { type: 'array', required: true, message: '请至少选择一个工单标签', trigger: 'change' }
  ],
  selectedClothes: [
    { type: 'array', required: true, message: '请至少勾选一件衣物', trigger: 'change' }
  ]
}

// 定义不同工单类型的标签
const ticketLabels = {
  '1': [ // 洗涤售后
    { label: '污渍不净', value: '1' },
    { label: '粘毛去球不干净', value: '2' },
    { label: '黄渍', value: '3' },
    { label: '羽绒成团', value: '4' },
    { label: '棉衣成团', value: '5' },
    { label: '附件洗坏', value: '6' },
    { label: '印花损坏', value: '7' },
    { label: '涂层损坏', value: '8' },
    { label: '勾丝', value: '9' },
    { label: '衣物损坏', value: '10' },
    { label: '脱绒', value: '11' },
    { label: '烫伤', value: '12' },
    { label: '褶皱', value: '13' },
    { label: '染色', value: '14' },
    { label: '褪色', value: '15' },
    { label: '虫蛀', value: '16' },
    { label: '翻毛皮鞋子洗后褪色', value: '17' },
    { label: '翻毛皮鞋子洗后发硬', value: '18' },
    { label: '掉漆', value: '19' },
    { label: '异味', value: '20' },
    { label: '衣物开线', value: '21' },
    { label: '缩水', value: '22' },
    { label: '变形', value: '23' },
    { label: '衣物丢失', value: '24' },
    { label: '附件丢失', value: '25' },
    { label: '超时未入厂', value: '26' },
    { label: '超时未出厂', value: '27' }
  ],
  '2': [ // 重大风险
    { label: '重大风险', value: '28' },
  ],
  '3': [ // 大牌补价
    { label: '大牌补价', value: '29' },
  ]
}

// 模拟衣物数据
const clothesData = ref([])

// 模拟水洗码对应的衣物数据
const mockClothesData = {
  'WS001': [
    {
      id: 1,
      washCode: 'WS001',
      name: '羽绒服',
      washMethod: '干洗',
      accessories: '帽子',
      defects: '无',
      shelfNumber: 'A01',
      image: '',
      status: '洗涤中',
      isRewash: false,
      stopWash: false,
      inFactoryTime: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      washCode: 'WS001',
      name: '西装外套',
      washMethod: '干洗',
      accessories: '领带',
      defects: '轻微污渍',
      shelfNumber: 'A02',
      image: '',
      status: '待洗涤',
      isRewash: false,
      stopWash: false,
      inFactoryTime: '2024-01-15 10:30:00'
    }
  ],
  'WS002': [
    {
      id: 3,
      washCode: 'WS002',
      name: '毛衣',
      washMethod: '水洗',
      accessories: '无',
      defects: '起球',
      shelfNumber: 'B01',
      image: '',
      status: '已完成',
      isRewash: true,
      stopWash: false,
      inFactoryTime: '2024-01-14 14:20:00'
    }
  ],
  'WS003': [
    {
      id: 4,
      washCode: 'WS003',
      name: '连衣裙',
      washMethod: '干洗',
      accessories: '腰带',
      defects: '拉链损坏',
      shelfNumber: 'C01',
      image: '',
      status: '质检中',
      isRewash: false,
      stopWash: true,
      inFactoryTime: '2024-01-13 16:45:00'
    },
    {
      id: 5,
      washCode: 'WS003',
      name: '丝巾',
      washMethod: '手洗',
      accessories: '无',
      defects: '无',
      shelfNumber: 'C02',
      image: '',
      status: '已完成',
      isRewash: false,
      stopWash: false,
      inFactoryTime: '2024-01-13 16:45:00'
    }
  ]
}

// 模拟用户数据
const mockUserData = {
  'WS001': {
    userName: '张三',
    userPhone: '13800138001',
    userAddress: '北京市朝阳区xxx街道xxx号',
    orderNo: 'ORD202401150001'
  },
  'WS002': {
    userName: '李四',
    userPhone: '13800138002',
    userAddress: '上海市浦东新区xxx路xxx号',
    orderNo: 'ORD202401140002'
  },
  'WS003': {
    userName: '王五',
    userPhone: '13800138003',
    userAddress: '广州市天河区xxx大道xxx号',
    orderNo: 'ORD202401130003'
  }
}

// 根据工单类型获取对应的标签
const currentLabels = computed(() => {
  return ticketLabels[sizeForm.workOrderType] || []
})

// 监听工单类型变化，清空已选标签
watch(() => sizeForm.workOrderType, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    sizeForm.labelId = []
  }
})

// 监听水洗码变化，清空衣物数据和用户信息
watch(() => sizeForm.washCode, (newVal) => {
  if (!newVal) {
    clothesData.value = []
    sizeForm.userName = ''
    sizeForm.userPhone = ''
    sizeForm.userAddress = ''
    sizeForm.orderNo = ''
    sizeForm.selectedClothes = []
  }
})

// 获取水洗码信息
const getWashInfo = async () => {
  if (!sizeForm.washCode) {
    ElMessage.warning('请先输入水洗码')
    return
  }

  washCodeLoading.value = true
  
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const clothes = mockClothesData[sizeForm.washCode]
    const userData = mockUserData[sizeForm.washCode]
    
    if (clothes && userData) {
      clothesData.value = clothes
      sizeForm.userName = userData.userName
      sizeForm.userPhone = userData.userPhone
      sizeForm.userAddress = userData.userAddress
      sizeForm.orderNo = userData.orderNo
      sizeForm.selectedClothes = [] // 清空之前的选择
      ElMessage.success('获取信息成功')
    } else {
      clothesData.value = []
      sizeForm.userName = ''
      sizeForm.userPhone = ''
      sizeForm.userAddress = ''
      sizeForm.orderNo = ''
      sizeForm.selectedClothes = []
      ElMessage.error('未找到对应的水洗码信息')
    }
  } catch (error) {
    ElMessage.error('获取信息失败，请重试')
  } finally {
    washCodeLoading.value = false
  }
}

// 处理衣物选择
const handleClothesSelection = (selection) => {
  sizeForm.selectedClothes = selection
}

// 全选衣物
const selectAllClothes = () => {
  // 这里需要通过ref获取table组件来调用toggleAllSelection方法
  // 由于Element Plus的限制，我们直接设置选中的衣物
  sizeForm.selectedClothes = [...clothesData.value]
}

// 清空选择
const clearAllClothes = () => {
  sizeForm.selectedClothes = []
}

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '待洗涤': 'info',
    '洗涤中': 'warning',
    '质检中': 'primary',
    '已完成': 'success',
    '异常': 'danger'
  }
  return statusMap[status] || 'info'
}

// 提交表单
const onSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 构建API参数
    const formData = {
      workOrderTitle: sizeForm.workOrderTitle,
      workOrderType: sizeForm.workOrderType,
      labelId: sizeForm.labelId.join(','), // 多个用逗号隔开
      washCode: sizeForm.washCode,
      priorityLevel: sizeForm.priorityLevel,
      credentialsUrl: credentialsUrl.value, // 上传凭证
      clothingId: sizeForm.selectedClothes.map(item => item.id).join(','), // 衣物ID，多个用逗号隔开
      orderNo: sizeForm.orderNo,
      workOrderStatus: sizeForm.workOrderStatus,
      remark: sizeForm.remark
    }
    
    console.log('提交的数据:', formData)
    
    // 调用API
    const response = await addInfo(formData)
    
    if (response.code === 200) {
      ElMessage.success('工单创建成功')
      // 可以在这里跳转到工单列表页面
      // this.$router.push('/customer/listTicket')
      
      // 重置表单
      resetForm()
    } else {
      ElMessage.error(response.msg || '工单创建失败')
    }
    
  } catch (error) {
    console.error('提交失败:', error)
    if (error.message && error.message.includes('validate')) {
      // 表单验证失败
      ElMessage.error('请检查表单信息')
    } else {
      ElMessage.error('工单创建失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  clothesData.value = []
  sizeForm.selectedClothes = []
  credentialsUrl.value = ''
}

const feature = ref()
</script>

<style scoped>
.page-container {
  min-height: calc(100vh - 90px);
  background-color: #f0f2f5;
  padding: 20px;
}

:deep(.el-upload--picture-card){
  height: 135px !important;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  height: 135px !important;
}
</style>
