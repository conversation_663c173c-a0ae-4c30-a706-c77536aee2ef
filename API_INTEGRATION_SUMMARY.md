# API接口与页面功能对接总结 (更新版)

## 已完成的修改

### 1. 导入API接口
在 `src/views/groupAdmin/index.vue` 中导入了以下API函数：

#### 客户管理API (customer.js)
- `listInfo` - 查询客户信息列表
- `addInfo` - 新增客户信息
- `updateInfo` - 修改客户信息
- `delInfo` - 删除客户信息

#### 用户管理API (user.js)
- `listUser` - 查询用户列表
- `addUser` - 新增用户
- `updateUser` - 修改用户
- `delUser` - 删除用户
- `changeUserStatus` - 修改用户状态

### 2. 数据加载函数
创建了两个主要的数据加载函数：

#### `loadCustomerList()`
- 调用 `listInfo()` API获取客户列表
- 将响应数据映射到 `groupTreeData`
- 自动调用 `loadUserList()` 加载用户列表数据
- 包含错误处理和loading状态管理

#### `loadUserList()`
- 调用 `listUser()` API获取系统用户列表
- 将响应数据映射到 `allUsers`
- 处理用户状态转换 ("0"正常 -> 'active', "1"停用 -> 'disabled')
- 自动更新客户的用户数量统计

### 3. CRUD操作API对接

#### 客户管理
- **新增客户**: `confirmAddGroup()` 调用 `addInfo()` API
- **编辑客户**: `confirmEditGroup()` 调用 `updateInfo()` API
- **删除客户**: `deleteGroup()` 调用 `delInfo()` API

#### 用户管理
- **新增用户**: `confirmAddUser()` 调用 `addUser()` API
- **编辑用户**: `confirmEditUser()` 调用 `updateUser()` API
- **删除用户**: `deleteUser()` 调用 `delUser()` API
- **切换用户状态**: `toggleUserStatus()` 调用 `changeUserStatus()` API

### 4. 数据映射
API响应数据到前端数据结构的映射：

#### 客户数据映射
```javascript
{
  id: item.id,
  name: item.name,
  description: item.description,
  level: 1,
  userCount: 0 // 通过用户关联数据计算
}
```

#### 用户数据映射
```javascript
{
  id: item.userId,
  name: item.nickName || item.userName,
  phone: item.phonenumber,
  balance: 0, // 用户表中没有余额字段，默认为0
  status: item.status === "0" ? 'active' : 'disabled', // "0"正常 "1"停用
  groupId: null, // 暂时设为null，后续可以通过部门或其他字段关联
  lastLoginTime: item.loginDate || new Date().toLocaleString(),
  avatar: item.avatar || '',
  email: item.email,
  dept: item.dept,
  createTime: item.createTime,
  updateTime: item.updateTime
}
```

### 5. 用户体验改进
- 添加了loading状态显示
- 所有操作完成后自动刷新数据
- 完善的错误处理和用户提示
- 保持了原有的UI交互逻辑

### 6. 初始化
在 `onMounted()` 生命周期中调用 `loadCustomerList()` 加载初始数据

## API接口对应关系

| 页面功能 | API接口 | 说明 |
|---------|---------|------|
| 加载客户列表 | `listInfo()` | 获取所有客户信息 |
| 新增客户 | `addInfo(data)` | 创建新客户 |
| 编辑客户 | `updateInfo(data)` | 更新客户信息 |
| 删除客户 | `delInfo(id)` | 删除指定客户 |
| 加载用户列表 | `listUser()` | 获取系统用户列表 |
| 新增用户 | `addUser(data)` | 创建系统用户 |
| 编辑用户 | `updateUser(data)` | 更新系统用户 |
| 删除用户 | `delUser(id)` | 删除系统用户 |
| 切换用户状态 | `changeUserStatus(id, status)` | 启用/禁用用户 |

## 注意事项
1. 所有API调用都包含了错误处理
2. 操作成功后会自动刷新相关数据
3. 保持了原有的用户交互体验
4. 添加了loading状态提升用户体验
5. API响应格式假设为 `{code: 200, data: [], msg: ''}`

## 重要变更说明

### 用户数据源变更
- **之前**: 使用客户-用户关联表 (`listRelation`, `addRelation` 等)
- **现在**: 使用系统用户表 (`listUser`, `addUser` 等)

### 数据字段映射变更
- **用户ID**: `item.id` → `item.userId`
- **用户名**: `item.userName` → `item.nickName`
- **手机号**: `item.userPhone` → `item.phonenumber`
- **状态**: `item.status` → `item.status === "0" ? 'active' : 'disabled'`
- **登录时间**: `item.lastLoginTime` → `item.loginDate`

### 新增功能
- 用户状态切换现在调用专门的 `changeUserStatus` API
- 新增用户时设置默认密码 "123456"
- 支持用户邮箱、部门等额外字段

现在页面已经完全与系统用户API对接，使用真实的用户管理接口。
